import org.springframework.boot.gradle.tasks.buildinfo.BuildInfo

plugins {
    kotlin("jvm")
    kotlin("kapt")
    id("org.jetbrains.kotlin.plugin.allopen")
    id("com.github.johnrengelman.shadow")
    id("io.micronaut.application")
    id("org.springframework.boot") version "2.7.12" apply false
}

val kotlinVersion: String by project
val micronautVersion: String by project
val pidCommonVersion: String  by project
val platformCommonVersion: String by project

val bankCoreApiClientVersion: String by project

kapt {
    arguments {
        arg("micronaut.openapi.views.spec", "redoc.enabled=true,rapidoc.enabled=true,swagger-ui.enabled=true,swagger-ui.theme=flattop")
    }
}

micronaut {
    version(micronautVersion)
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("cz.partners.*")
    }
    enableNativeImage(true)

}

application {
    mainClass.set("cz.partners.bank.rabbitgateway.Application")
}

java {
    sourceCompatibility = JavaVersion.VERSION_11
}

tasks.test {
    useJUnitPlatform()
}

val buildInfo = tasks.register<BuildInfo>("buildInfo") {
    description = "Generates build-info.properties file."

    group = BasePlugin.BUILD_GROUP

    destinationDir = File(sourceSets.main.get().output.resourcesDir, "META-INF")

    properties {
        time = null // Otherwise task is never up-to-date
        this.additional["pidCommonVersion"] = pidCommonVersion
this.additional["platformCommonVersion"] = platformCommonVersion
this.additional["uhcVersion"] = System.getenv("UHC_VER").orEmpty()

        this.name = project.rootProject.name
    }
}

tasks.classes {
    dependsOn(buildInfo)
}

dependencies {
    kapt(platform("cz.pbktechnology.platform.common:bom:$platformCommonVersion"))
    implementation(platform("cz.pbktechnology.platform.common:bom:$platformCommonVersion"))

    implementation(project(":rabbitapi"))
    implementation("cz.pbktechnology.platform.common:api:$platformCommonVersion")
    implementation("cz.pbktechnology.platform.common:rabbitmq:$platformCommonVersion")
    implementation("cz.pbktechnology.platform.common:utils:$platformCommonVersion")
    implementation("cz.pbktechnology.platform.common:logging-logback:$platformCommonVersion")

    implementation("cz.partners.bank:bank-core-api-api:$bankCoreApiClientVersion")

    kapt("io.micronaut:micronaut-inject-java")
    kapt("io.micronaut:micronaut-validation")
    kapt("io.micronaut.data:micronaut-data-processor")
    kapt("io.micronaut.openapi:micronaut-openapi")

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactive")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-jdk8")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j")

    implementation(kotlin("stdlib"))
    implementation("javax.annotation:javax.annotation-api")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("io.micronaut.kotlin:micronaut-kotlin-runtime")
    implementation("io.micronaut.kotlin:micronaut-kotlin-extension-functions")

    implementation("io.micronaut:micronaut-validation")
    implementation("io.micronaut:micronaut-runtime")
    implementation("io.micronaut:micronaut-http-client")
    implementation("io.micronaut:micronaut-inject")
    implementation("io.micronaut.reactor:micronaut-reactor")
    implementation("io.micronaut.reactor:micronaut-reactor-http-client")
    implementation("io.micronaut:micronaut-http-server-netty")


    implementation("io.micronaut:micronaut-management")
    implementation("io.micronaut.micrometer:micronaut-micrometer-core")
    implementation("io.micronaut.micrometer:micronaut-micrometer-registry-prometheus")

    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    // DATABASE + ORACLE packages
    implementation("io.micronaut.sql:micronaut-jdbc-hikari")
    implementation("io.micronaut.data:micronaut-data-jdbc")
    implementation("com.oracle.database.jdbc:ojdbc8")
    implementation("com.oracle.database.messaging:aqapi:********")

    implementation("io.swagger.core.v3:swagger-annotations")
    implementation("io.swagger.core.v3:swagger-core")

    //ZIPKIN
    implementation("io.micronaut.tracing:micronaut-tracing-core")
    implementation("io.micronaut.tracing:micronaut-tracing-zipkin")
    implementation("io.opentracing.brave:brave-opentracing")
    runtimeOnly("io.zipkin.reporter2:zipkin-reporter")

    implementation("io.arrow-kt:arrow-core")
    implementation("com.rabbitmq:amqp-client")

    // this fixes logger start issue
    implementation("xerces:xercesImpl")

    testAnnotationProcessor("io.micronaut:micronaut-inject-java")
    testImplementation("cz.pbktechnology.platform.common:rabbitmq-test:$platformCommonVersion")
    testImplementation("cz.pbktechnology.platform.common:test-utils:$platformCommonVersion")
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.mockito:mockito-inline")
    testImplementation("org.junit.jupiter:junit-jupiter-params")
    testImplementation("org.mockito.kotlin:mockito-kotlin")
    testImplementation("io.mockk:mockk")
    testImplementation("com.github.fridujo:rabbitmq-mock")
    testImplementation("org.testcontainers:testcontainers")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:oracle-free:1.20.0")
}
