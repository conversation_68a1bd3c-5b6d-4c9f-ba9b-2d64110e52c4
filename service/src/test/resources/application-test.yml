micronaut:
  server:
    port: -1
looper:
  required:
    config:
      enabled: false
  realized:
    config:
      enabled: false
  card-hold:
    config:
      enabled: false
  record-version-change:
    config:
      enabled: false
  suspected-fraud:
    config:
      enabled: false
  planned-transactions:
    config:
      enabled: false
  outgoing-message:
    config:
      enabled: false

datasources:
  default:
    dialect: ORACLE
    driverClassName: oracle.jdbc.OracleDriver
