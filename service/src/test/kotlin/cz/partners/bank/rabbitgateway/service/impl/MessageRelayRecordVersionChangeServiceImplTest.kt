package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import cz.partners.bank.core.api.TransactionType.DIRECT_DEBIT
import cz.partners.bank.core.api.TransactionType.STANDING_ORDER
import cz.partners.bank.rabbitgateway.TestHelper
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.producer.AccountProducer
import cz.partners.bank.rabbitgateway.producer.IncomingMessage
import cz.partners.bank.rabbitgateway.producer.LoanProducer
import cz.partners.bank.rabbitgateway.producer.RecordVersionChangeIncomingProducer
import cz.partners.bank.rabbitgateway.producer.TerminationAccountProducer
import cz.partners.bank.rabbitgateway.producer.TransactionProducer
import cz.partners.bank.rabbitgateway.rabbitapi.AccountVersionChangeMessage
import cz.partners.bank.rabbitgateway.rabbitapi.TransactionVersionChangeMessage
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.partners.bank.rabbitgateway.service.impl.MessageRelayRecordVersionChangeServiceImpl.Companion.RECORD_VERSION_CHANGE_CORE_QUEUE
import cz.pbktechnology.platform.common.exception.IoPbkException
import cz.pbktechnology.platform.common.test.util.EitherTestExtension.assertIsLeft
import cz.pbktechnology.platform.common.test.util.GeneralTestExtension.assertExceptionNameContains
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class MessageRelayRecordVersionChangeServiceImplTest {
    @MockK
    private lateinit var oracleDataSource: OracleDataSource

    @MockK
    private lateinit var oracleAqJmsService: OracleAqJmsService

    @MockK
    private lateinit var transactionProducer: TransactionProducer

    @MockK
    private lateinit var accountProducer: AccountProducer

    @MockK
    private lateinit var terminationAccountProducer: TerminationAccountProducer

    @MockK
    private lateinit var loanProducer: LoanProducer

    @MockK
    private lateinit var recordVersionChangeIncomingProducer: RecordVersionChangeIncomingProducer

    private val dcsAccountProcessingEnabled = true

    private val dcsAccountTerminationProcessingEnabled = true

    @InjectMockKs
    private lateinit var service: MessageRelayRecordVersionChangeServiceImpl

    @Suppress("unused") // object mapper must be initialised to be able to instantiate service
    private val objectMapper = ObjectMapper().registerKotlinModule()
        .findAndRegisterModules()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    @Test
    fun `Does not send a any message for standing order when dequeue fails`() = runBlocking {
        val fail = IoPbkException("Can not dequeue message", "abc", null).left()
        coEvery {
            oracleDataSource.dequeueMessage(RECORD_VERSION_CHANGE_CORE_QUEUE, any(), any())
        } returns fail

        coVerify(exactly = 0) {
            recordVersionChangeIncomingProducer.publish(any())
            transactionProducer.recordVersionChangeStandingOrder(any())
            transactionProducer.recordVersionChangeDirectDebit(any())
            transactionProducer.recordVersionChangeSipo(any())
            accountProducer.recordVersionChangeAccount(any())
            terminationAccountProducer.recordVersionChangeAccount(any())
            loanProducer.recordVersionChangeLoan(any())
        }

        val result = Either.catch {
            service.dequeueAndSendToIncomingMessage("0", 20)
        }
        assertEquals(fail, result)
    }

    @Test
    fun `Relay fails for standing order on parse error`() = runBlocking {
        val receivedString = "core-bank invalid message"

        val slot = slot<IncomingMessage>()

        coEvery {
            recordVersionChangeIncomingProducer.publish(capture(slot))
        } returns Unit
        coVerify(exactly = 0) {
            transactionProducer.recordVersionChangeStandingOrder(any())
            transactionProducer.recordVersionChangeSipo(any())
            transactionProducer.recordVersionChangeDirectDebit(any())
            accountProducer.recordVersionChangeAccount(any())
            terminationAccountProducer.recordVersionChangeAccount(any())
            loanProducer.recordVersionChangeLoan(any())
        }

        val result = service.relayMessage(receivedString)

        result.assertIsLeft()
            .assertExceptionNameContains(QueueParseException::class.simpleName!!)
    }

    @Test
    fun `Relay fails on invalid entity type for standing order`() = runBlocking {
        val receivedString = TestHelper.recordVersionChangeNewTransactionTypeJson()

        val slot = slot<IncomingMessage>()

        coEvery {
            recordVersionChangeIncomingProducer.publish(capture(slot))
        } returns Unit
        coVerify(exactly = 0) {
            transactionProducer.recordVersionChangeStandingOrder(any())
            transactionProducer.recordVersionChangeSipo(any())
            transactionProducer.recordVersionChangeDirectDebit(any())
            accountProducer.recordVersionChangeAccount(any())
            terminationAccountProducer.recordVersionChangeAccount(any())
            loanProducer.recordVersionChangeLoan(any())
        }

        val result = service.relayMessage(receivedString)

        result.assertIsLeft()
            .assertExceptionNameContains(QueueParseException::class.simpleName!!)
    }

    @Test
    fun `Does send a record version change transaction type standing order on successful parsing`() = runBlocking {
        val receivedString = TestHelper.recordVersionChangeValidStandingOrderJson()

        val slot = slot<TransactionVersionChangeMessage>()

        coEvery {
            transactionProducer.recordVersionChangeStandingOrder(capture(slot))
        } returns Unit.right()
        coVerify(exactly = 0) {
            recordVersionChangeIncomingProducer.publish(any())
        }

        service.relayMessage(receivedString)
        val captured = slot.captured
        Assertions.assertNotNull(captured)
        assertEquals(STANDING_ORDER, slot.captured.transactionType)
    }

    @Test
    fun `Does send a record version change transaction type direct debit on successful parsing`() = runBlocking {
        val receivedString = TestHelper.recordVersionChangeValidDirectDebitJson()

        val slot = slot<TransactionVersionChangeMessage>()

        coEvery {
            transactionProducer.recordVersionChangeDirectDebit(capture(slot))
        } returns Unit.right()
        coVerify(exactly = 0) {
            recordVersionChangeIncomingProducer.publish(any())
        }

        service.relayMessage(receivedString)
        val captured = slot.captured
        Assertions.assertNotNull(captured)
        assertEquals(DIRECT_DEBIT, slot.captured.transactionType)
    }

    @Test
    fun `Does send a record version change type account change on successful parsing`() = runBlocking {
        val receivedString = TestHelper.recordVersionChangeAccountJson()

        val slot = slot<AccountVersionChangeMessage>()

        coEvery {
            accountProducer.recordVersionChangeAccount(capture(slot))
        } returns Unit.right()

        coEvery {
            terminationAccountProducer.recordVersionChangeAccount(capture(slot))
        } returns Unit.right()

        service.relayMessage(receivedString)

        coVerify(exactly = 1) {
            accountProducer.recordVersionChangeAccount(any())
        }
        coVerify(exactly = 1) {
            terminationAccountProducer.recordVersionChangeAccount(any())
        }
        coVerify(exactly = 0) {
            recordVersionChangeIncomingProducer.publish(any())
        }

        val captured = slot.captured
        Assertions.assertNotNull(captured)
        assertEquals(1000034, slot.captured.coreId)
        assertNull(slot.captured.totalSettlementAmount)
    }

//    TODO az po implementaci SIPO transakci
//    @Test
//    fun `Does send a record version change transaction type sipo on successful parsing`() = runBlocking {
//        val receivedString = TestHelper.recordVersionChangeValidSipoJson()
//
//        val slot = slot<RecordVersionChangeMessage>()
//
//        coEvery {
//            producer.recordVersionChangeSipo(capture(slot))
//        } returns Unit.right()
//        coVerify(exactly = 0) {
//            producerFailed.publishToFailedQueue(any())
//        }
//
//        service.relayMessage(receivedString)
//        val captured = slot.captured
//        Assertions.assertNotNull(captured)
//        assertEquals(TransactionType.SIPO, slot.captured.transactionType)
//        println(captured)
//    }
}
