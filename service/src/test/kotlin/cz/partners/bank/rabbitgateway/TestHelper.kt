package cz.partners.bank.rabbitgateway

object TestHelper {

    fun validRequiredTransactionJson() = readResource("/json/required/valid.json")
    fun requiredTransactionWithInvalidAccountNumberJson() = readResource("/json/required/invalidAccountNumber.json")

    fun validRealizedTransactionJson() = readResource("/json/realized/valid.json")
    fun valid2RealizedTransactionJson() = readResource("/json/realized/valid2.json")
    fun valid3RealizedTransactionJson() = readResource("/json/realized/valid3.json")
    fun validRealizedTransactionWithCreditBalanceJson() = readResource("/json/realized/validWithCreditBalance.json")
    fun validRealizedTransactionWithDebitBalanceJson() = readResource("/json/realized/validWithDebitBalance.json")
    fun realizedTransactionWithInvalidAccountNumberJson() = readResource("/json/realized/invalidAccountNumber.json")

    fun validCardHoldWithoutBalanceJson() = readResource("/json/cardHold/cardWithdrawal.json")
    fun validCardHoldWithoutAccountName() = readResource("/json/cardHold/cardHoldWithoutAccountName.json")

    fun recordVersionChangeNewTransactionTypeJson() = readResource("/json/recordVersionChange/recordVersionChangeNewTransactionType.json")
    fun recordVersionChangeValidDirectDebitJson() = readResource("/json/recordVersionChange/recordVersionChangeValidDirectDebit.json")
    fun recordVersionChangeValidStandingOrderJson() = readResource("/json/recordVersionChange/recordVersionChangeValidStandingOrder.json")
    fun recordVersionChangeValidSipoJson() = readResource("/json/recordVersionChange/recordVersionChangeValidSipo.json")
    fun recordVersionChangeAccountJson() = readResource("/json/recordVersionChange/recordVersionChangeAccount.json")
    fun accountChangeJson() = readResource("/json/recordVersionChange/accountChange.json")

    fun suspectedFraudJson() = readResource("/json/suspectedFraud/suspectedFraud.json")
    fun plannedTransactionsJson() = readResource("/json/plannedTransactions/plannedTransactions.json")
    fun reducedInterestRateOutgoingMessageJson() = readResource("/json/outgoing-message/reduced-interest-rate.json")
    fun loanPaidValuesChangeOutgoingMessageJson() = readResource("/json/outgoing-message/loan-paid-values-change.json")
    fun loanPastDueChangeOutgoingMessageJson() = readResource("/json/outgoing-message/loan-past-due-change.json")
    fun unsupportedOutgoingMessageJson() = readResource("/json/outgoing-message/unsupported-message.json")

    private fun readResource(
        path: String,
    ): String = this::class.java.getResource(path)
        .readText()

}
