package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.right
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import cz.partners.bank.rabbitgateway.TestHelper
import cz.partners.bank.rabbitgateway.producer.AccountChangesIncomingProducer
import cz.partners.bank.rabbitgateway.producer.AccountProducer
import cz.partners.bank.rabbitgateway.producer.TerminationAccountProducer
import cz.partners.bank.rabbitgateway.rabbitapi.AccountVersionChangeMessage
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import java.math.BigDecimal
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class MessageRelayAccountChangesServiceImplTest {
    @MockK
    private lateinit var oracleDataSource: OracleDataSource

    @MockK
    private lateinit var accountProducer: AccountProducer

    @MockK
    private lateinit var terminationAccountProducer: TerminationAccountProducer

    @MockK
    private lateinit var accountChangesIncomingProducer: AccountChangesIncomingProducer

    @MockK
    private lateinit var oracleAqJmsService: OracleAqJmsService

    @InjectMockKs
    private lateinit var service: MessageRelayAccountChangesServiceImpl

    private val dcsAccountTerminationProcessingEnabled = false

    @Suppress("unused") // object mapper must be initialised to be able to instantiate service
    private val objectMapper = ObjectMapper().registerKotlinModule()
        .findAndRegisterModules()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    @Test
    fun `Does send a account change on successful parsing`() =
        runBlocking {
            val receivedString = TestHelper.accountChangeJson()

            val slot = slot<AccountVersionChangeMessage>()

            coEvery {
                accountProducer.recordVersionChangeAccount(capture(slot))
            } returns Unit.right()

            coEvery {
                terminationAccountProducer.recordVersionChangeAccount(capture(slot))
            } returns Unit.right()

            service.relayMessage(receivedString)

            coVerify(exactly = 1) {
                accountProducer.recordVersionChangeAccount(any())
            }
            coVerify(exactly = 0) {
                terminationAccountProducer.recordVersionChangeAccount(any())
            }
            coVerify(exactly = 0) {
                accountChangesIncomingProducer.publish(any())
            }
            val captured = slot.captured
            Assertions.assertNotNull(captured)
            assertEquals(1000034, slot.captured.coreId)
            assertEquals("ACTIVE", slot.captured.status)
            assertEquals(BigDecimal(3), slot.captured.totalSettlementAmount)
        }
}