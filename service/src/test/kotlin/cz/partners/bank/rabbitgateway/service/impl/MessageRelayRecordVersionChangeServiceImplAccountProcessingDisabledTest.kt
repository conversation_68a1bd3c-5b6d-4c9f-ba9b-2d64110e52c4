package cz.partners.bank.rabbitgateway.service.impl

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import cz.partners.bank.rabbitgateway.TestHelper
import cz.partners.bank.rabbitgateway.producer.AccountProducer
import cz.partners.bank.rabbitgateway.producer.LoanProducer
import cz.partners.bank.rabbitgateway.producer.RecordVersionChangeIncomingProducer
import cz.partners.bank.rabbitgateway.producer.TerminationAccountProducer
import cz.partners.bank.rabbitgateway.producer.TransactionProducer
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import io.mockk.coVerify
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class MessageRelayRecordVersionChangeServiceImplAccountProcessingDisabledTest {
    @MockK
    private lateinit var oracleDataSource: OracleDataSource

    @MockK
    private lateinit var transactionProducer: TransactionProducer

    @MockK
    private lateinit var accountProducer: AccountProducer

    @MockK
    private lateinit var terminationAccountProducer: TerminationAccountProducer

    @MockK
    private lateinit var loanProducer: LoanProducer

    @MockK
    private lateinit var recordVersionChangeIncomingProducer: RecordVersionChangeIncomingProducer

    @MockK
    private lateinit var oracleAqJmsService: OracleAqJmsService

    private val dcsAccountProcessingEnabled = false

    private val dcsAccountTerminationProcessingEnabled = false

    @InjectMockKs
    private lateinit var service: MessageRelayRecordVersionChangeServiceImpl

    @Suppress("unused") // object mapper must be initialised to be able to instantiate service
    private val objectMapper = ObjectMapper().registerKotlinModule()
        .findAndRegisterModules()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    @Test
    fun `Does not send a record version change type account change on successful parsing but processing disabled`() =
        runBlocking {
            val receivedString = TestHelper.recordVersionChangeAccountJson()

            service.relayMessage(receivedString)

            coVerify(exactly = 0) {
                recordVersionChangeIncomingProducer.publish(any())
                transactionProducer.recordVersionChangeStandingOrder(any())
                transactionProducer.recordVersionChangeSipo(any())
                transactionProducer.recordVersionChangeDirectDebit(any())
                accountProducer.recordVersionChangeAccount(any())
                terminationAccountProducer.recordVersionChangeAccount(any())
                loanProducer.recordVersionChangeLoan(any())
            }
        }
}