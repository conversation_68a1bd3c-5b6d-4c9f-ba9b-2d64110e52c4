package cz.partners.bank.rabbitgateway.service

import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.ContextProvider
import cz.pbktechnology.platform.common.context.OperationContext
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import java.util.UUID

/**
 * Utility class for managing context propagation across JMS threads.
 * Handles capture and restoration of OperationContext, MDC, and OpenTelemetry context.
 */
@Singleton
class JmsContextManager(private val contextProvider: ContextProvider) {
    
    private val logger = LoggerFactory.getLogger(JmsContextManager::class.java)
    
    /**
     * Captured context data that can be transferred across threads
     */
    data class CapturedContext(
        val operationContext: OperationContext?,
        val mdcContext: Map<String, String>,
        val otelContext: Any?
    )
    
    /**
     * Captures the current thread's context for later restoration
     * 
     * @return CapturedContext containing all context elements
     */
    fun captureCurrentContext(): CapturedContext {
        return CapturedContext(
            operationContext = ContextConfiguration.operationContext.get(),
            mdcContext = MDC.getCopyOfContextMap() ?: emptyMap(),
            otelContext = ContextConfiguration.openTelemetryThreadLocalContext.get()
        )
    }
    
    /**
     * Executes a block with the captured context restored on the current thread.
     * Automatically restores the original context after execution.
     *
     * @param captured The context to restore
     * @param block The code to execute with the restored context
     * @return The result of the block execution
     */
    fun <T> withContext(captured: CapturedContext, block: () -> T): T {
        // Capture original context to restore later
        val originalOperationContext = ContextConfiguration.operationContext.get()
        val originalMdcContext = MDC.getCopyOfContextMap()
        val originalOtelContext = ContextConfiguration.openTelemetryThreadLocalContext.get()

        try {
            // Set captured context
            restoreContext(captured)

            return block()
        } finally {
            // Restore original context
            restoreOriginalContext(originalOperationContext, originalMdcContext, originalOtelContext)
        }
    }
    
    /**
     * Restores the captured context to the current thread
     */
    private fun restoreContext(captured: CapturedContext) {
        // 1. Set OperationContext - create new one if none was captured
        val contextToUse = captured.operationContext ?: OperationContext(UUID.randomUUID())
        ContextConfiguration.operationContext.set(contextToUse)
        
        // 2. Set MDC context
        if (captured.mdcContext.isNotEmpty()) {
            MDC.setContextMap(captured.mdcContext)
        } else {
            MDC.clear()
        }
        
        // 3. Set OpenTelemetry context
        captured.otelContext?.let { context ->
            try {
                ContextConfiguration.openTelemetryThreadLocalContext.set(context)
                // Note: We can't call makeCurrent() without proper OpenTelemetry dependency
                // This still ensures the ThreadLocal is set for any code that needs it
            } catch (e: Exception) {
                logger.trace("Could not set OpenTelemetry context", e)
            }
        }

        logger.trace("Restored context - OperationId: ${contextToUse.id}, MDC keys: ${captured.mdcContext.keys}")
    }
    
    /**
     * Restores the original context that was present before withContext was called
     */
    private fun restoreOriginalContext(
        originalOperationContext: OperationContext?,
        originalMdcContext: Map<String, String>?,
        originalOtelContext: Any?
    ) {
        // Restore OperationContext
        if (originalOperationContext != null) {
            ContextConfiguration.operationContext.set(originalOperationContext)
        } else {
            ContextConfiguration.operationContext.remove()
        }
        
        // Restore MDC context
        if (originalMdcContext != null && originalMdcContext.isNotEmpty()) {
            MDC.setContextMap(originalMdcContext)
        } else {
            MDC.clear()
        }
        
        // Restore OpenTelemetry context
        try {
            if (originalOtelContext != null) {
                ContextConfiguration.openTelemetryThreadLocalContext.set(originalOtelContext)
            } else {
                ContextConfiguration.openTelemetryThreadLocalContext.remove()
            }
        } catch (e: Exception) {
            logger.trace("Could not restore OpenTelemetry context", e)
        }
        
        logger.trace("Restored original context")
    }
    
    /**
     * Cleans up all context elements from the current thread.
     * Useful for cleanup in finally blocks or error scenarios.
     */
    fun clearContext() {
        ContextConfiguration.operationContext.remove()
        ContextConfiguration.openTelemetryThreadLocalContext.remove()
        MDC.clear()
        logger.trace("Cleared all context from current thread")
    }
    
    /**
     * Creates a new context with a fresh OperationContext while preserving MDC and OpenTelemetry context
     * 
     * @param operationName Name to include in logging context
     * @return CapturedContext with new OperationContext
     */
    fun createFreshContext(operationName: String): CapturedContext {
        val newOperationContext = OperationContext(UUID.randomUUID())
        
        // Add operation name to MDC for better logging
        val currentMdc = MDC.getCopyOfContextMap() ?: mutableMapOf()
        val enhancedMdc = currentMdc.toMutableMap().apply {
            put("operation", operationName)
        }
        
        return CapturedContext(
            operationContext = newOperationContext,
            mdcContext = enhancedMdc,
            otelContext = ContextConfiguration.openTelemetryThreadLocalContext.get()
        )
    }
}
