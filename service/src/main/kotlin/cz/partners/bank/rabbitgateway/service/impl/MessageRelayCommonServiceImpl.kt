package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.databind.ObjectMapper
import cz.partners.bank.rabbitgateway.exception.NoMessageReceivedException
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.producer.IncomingMessage
import cz.partners.bank.rabbitgateway.producer.IncomingProducer
import cz.partners.bank.rabbitgateway.service.MessageRelayService
import cz.partners.bank.rabbitgateway.service.MessageProcessingResult
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.pbktechnology.platform.common.exception.PbkException
import org.slf4j.Logger
import javax.jms.Message
import javax.jms.TextMessage
import javax.transaction.Transactional

abstract class MessageRelayCommonServiceImpl<TransactionDto>(
    private val incomingProducer: IncomingProducer,
    val objectMapper: ObjectMapper,
    protected val oracleDataSource: OracleDataSource,
    protected val oracleAqJmsService: OracleAqJmsService
) : MessageRelayService {
    protected abstract val logger: Logger
    
    /**
     * The queue name to consume from
     * Must be overridden by subclasses
     */
    protected abstract val queueName: String
    


    @Transactional
    override fun dequeueAndSendToIncomingMessage(
        instance: String,
        timeoutSeconds: Int,
    ) {
        dequeueMessage(instance, timeoutSeconds)
            .fold(
                { error ->
                    processException(error)
                },
                { message ->
                    incomingProducer.publish(
                        IncomingMessage(message)
                    )
                }
            )
    }

    /**
     * Starts a reactive consumer for the Oracle AQ JMS queue
     * This method starts listening to the queue using the provided context
     *
     * @param instance The instance identifier for logging
     */
    suspend fun startReactiveConsumer(instance: String) {
        logger.info("[$instance] Starting reactive consumer for queue [$queueName]")

        oracleAqJmsService.listenToQueue(queueName, instance) { message ->
            processMessage(message, instance)
        }
    }

    /**
     * Process a JMS message received from the queue
     * Returns Success or Failure - failures will be retried automatically
     */
    private fun processMessage(message: Message, instance: String): MessageProcessingResult {
        return try {
            logger.debug("[$instance] Processing message from queue [$queueName]: ${message.jmsMessageID}")

            if (message is TextMessage) {
                val messageText = message.text
                logger.debug("[$instance] Received AQ JMS message from [$queueName]: $messageText")

                // Context will be set up by OracleAqJmsService message listener
                incomingProducer.publish(IncomingMessage(messageText))

                MessageProcessingResult.Success
            } else {
                // Non-text messages are also treated as failures and will be retried
                logger.warn("[$instance] Received non-text message from [$queueName]: ${message.jmsMessageID}, will retry")
                MessageProcessingResult.Failure(IllegalArgumentException("Non-text message type: ${message.javaClass.simpleName}"))
            }
        } catch (e: Exception) {
            logger.error("[$instance] Error processing message from queue [$queueName]: ${message.jmsMessageID}", e)
            MessageProcessingResult.Failure(e)
        }
    }

    override fun relayMessage(
        message: String,
    ): Either<PbkException, Unit> = unmarshalResult(message)
        .map { unmarshalledMessage ->
            produceResult(unmarshalledMessage)
        }

    abstract fun unmarshalResult(rawResult: String): Either<QueueParseException, TransactionDto>

    abstract fun dequeueMessage(instance: String, timeoutSeconds: Int): Either<PbkException, String>

    abstract fun produceResult(message: TransactionDto): Either<PbkException, Unit>

    protected inline fun <reified Type> unmarshalResultHelper(
        rawResult: String,
    ): Either<QueueParseException, Type> {
        return Either.catch {
            objectMapper.readValue(rawResult, Type::class.java)
        }.mapLeft { QueueParseException(rawResult, it) }
    }

    private fun processException(error: Throwable): Either<PbkException, Unit> {
        when (error) {
            is NoMessageReceivedException -> logger.debug("No message received [${error.sourceQueue}] [${error.instance}]")
            else -> {
                logger.error("relay ${this.javaClass} failed", error)
                throw error // to revert transaction
            }
        }
        return Unit.right()
    }
}
