package cz.partners.bank.rabbitgateway.configuration

import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Factory
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import oracle.jms.AQjmsFactory
import java.util.Properties
import javax.jms.QueueConnection
import javax.jms.QueueConnectionFactory

/**
 * Configuration properties for default datasource
 */
@ConfigurationProperties("datasources.default")
class DataSourceSettings {
    lateinit var url: String
    lateinit var username: String
    lateinit var password: String
}

@Factory
class OracleAqJmsConfiguration(private val dataSourceSettings: DataSourceSettings) {
    private val logger = LoggerFactory.getLogger(OracleAqJmsConfiguration::class.java)

    @Singleton
    fun queueConnectionFactory(): QueueConnectionFactory {
        logger.info("Creating Oracle AQ JMS connection factory with URL: ${dataSourceSettings.url}")
        val props = Properties().apply {
            put("user", dataSourceSettings.username)
            put("password", dataSourceSettings.password)
        }
        return AQjmsFactory.getQueueConnectionFactory(dataSourceSettings.url, props)
    }

    @Singleton
    fun queueConnection(factory: QueueConnectionFactory): QueueConnection {
        logger.info("Creating Oracle AQ JMS queue connection")
        val connection = factory.createQueueConnection()
        connection.start()
        return connection
    }
    
    @Singleton
    fun oracleAqSchema(): String {
        // Using the same schema as the username
        return dataSourceSettings.username
    }
}