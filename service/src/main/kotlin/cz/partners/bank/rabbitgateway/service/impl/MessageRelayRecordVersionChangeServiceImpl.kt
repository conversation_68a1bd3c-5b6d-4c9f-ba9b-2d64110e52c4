package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.databind.ObjectMapper
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.model.core.EntityType.COL_SIPOCOLLECTION
import cz.partners.bank.rabbitgateway.model.core.EntityType.DCS_ACCOUNT
import cz.partners.bank.rabbitgateway.model.core.EntityType.DCS_COLLECTION
import cz.partners.bank.rabbitgateway.model.core.EntityType.DCS_LOAN
import cz.partners.bank.rabbitgateway.model.core.EntityType.DCS_REPEATEDTRANSACTION
import cz.partners.bank.rabbitgateway.model.core.RecordVersionChangeCore
import cz.partners.bank.rabbitgateway.producer.AccountProducer
import cz.partners.bank.rabbitgateway.producer.LoanProducer
import cz.partners.bank.rabbitgateway.producer.RecordVersionChangeIncomingProducer
import cz.partners.bank.rabbitgateway.producer.TerminationAccountProducer
import cz.partners.bank.rabbitgateway.producer.TransactionProducer
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bindSync
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Singleton
open class MessageRelayRecordVersionChangeServiceImpl(
    private val transactionProducer: TransactionProducer,
    private val accountProducer: AccountProducer,
    private val terminationAccountProducer: TerminationAccountProducer,
    private val loanProducer: LoanProducer,
    failedProducer: RecordVersionChangeIncomingProducer,
    objectMapper: ObjectMapper,
    oracleDataSource: OracleDataSource,
    oracleAqJmsService: OracleAqJmsService,
    @Value("\${bank.record-version-change.dcs-account.processing-enabled}")
    val dcsAccountProcessingEnabled: Boolean,
    @Value("\${bank.record-version-change.dcs-account.termination-processing-enabled}")
    val dcsAccountTerminationProcessingEnabled: Boolean,
) : MessageRelayCommonServiceImpl<RecordVersionChangeCore>(
    failedProducer,
    objectMapper,
    oracleDataSource,
    oracleAqJmsService
) {
    override val logger: Logger = LoggerFactory.getLogger(this::class.java)
    
    override val queueName: String = RECORD_VERSION_CHANGE_CORE_QUEUE

    override fun dequeueMessage(
        instance: String,
        timeoutSeconds: Int,
    ): Either<PbkException, String> = oracleDataSource.dequeueMessage(
        queueName = RECORD_VERSION_CHANGE_CORE_QUEUE,
        instance = instance,
        timeoutSeconds = timeoutSeconds
    )

    override fun unmarshalResult(rawResult: String): Either<QueueParseException, RecordVersionChangeCore> = bindSync {
        !unmarshalResultHelper<RecordVersionChangeCore>(rawResult)
    }

    override fun produceResult(message: RecordVersionChangeCore): Either<PbkException, Unit> = bindSync {
        logger.info("Received record change version for entity [${message.entityId}]")

        when (message.entity) {
            DCS_REPEATEDTRANSACTION -> transactionProducer.recordVersionChangeStandingOrder(message.toTransactionMessage())
            DCS_COLLECTION -> transactionProducer.recordVersionChangeDirectDebit(message.toTransactionMessage())
            COL_SIPOCOLLECTION -> transactionProducer.recordVersionChangeSipo(message.toTransactionMessage())
            DCS_ACCOUNT -> {
                if (dcsAccountProcessingEnabled) {
                    !accountProducer.recordVersionChangeAccount(message.toAccountMessage())

                    if (dcsAccountTerminationProcessingEnabled) !terminationAccountProducer.recordVersionChangeAccount(message.toAccountMessage())
                }
                Unit.right()
            }

            DCS_LOAN -> loanProducer.recordVersionChangeLoan(message.toLoanMessage())
        }
    }

    companion object {
        const val RECORD_VERSION_CHANGE_CORE_QUEUE = "RECORD_VERSION_CHANGE"
    }
}