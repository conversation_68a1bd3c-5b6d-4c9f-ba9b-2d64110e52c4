package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.model.core.AccountChangesCore
import cz.partners.bank.rabbitgateway.producer.AccountChangesIncomingProducer
import cz.partners.bank.rabbitgateway.producer.AccountProducer
import cz.partners.bank.rabbitgateway.producer.TerminationAccountProducer
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bindSync
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton
import org.slf4j.Logger
import org.slf4j.LoggerFactory

const val ACCOUNT_CHANGES_CORE_QUEUE = "ACCOUNT_CHANGES"

@Singleton
open class MessageRelayAccountChangesServiceImpl(
    private val accountProducer: AccountProducer,
    private val terminationAccountProducer: TerminationAccountProducer,
    incomingProducer: AccountChangesIncomingProducer,
    objectMapper: ObjectMapper,
    oracleDataSource: OracleDataSource,
    oracleAqJmsService: OracleAqJmsService,
    @Value("\${bank.record-version-change.dcs-account.termination-processing-enabled}")
    val dcsAccountTerminationProcessingEnabled: Boolean,
) : MessageRelayCommonServiceImpl<AccountChangesCore>(
    incomingProducer = incomingProducer,
    objectMapper = objectMapper,
    oracleDataSource = oracleDataSource,
    oracleAqJmsService = oracleAqJmsService
) {
    override val logger: Logger = LoggerFactory.getLogger(this::class.java)
    
    override val queueName: String = ACCOUNT_CHANGES_CORE_QUEUE

    override fun dequeueMessage(instance: String, timeoutSeconds: Int): Either<PbkException, String> = oracleDataSource.dequeueMessage(
        queueName = ACCOUNT_CHANGES_CORE_QUEUE,
        instance = instance,
        timeoutSeconds = timeoutSeconds
    )

    override fun unmarshalResult(rawResult: String): Either<QueueParseException, AccountChangesCore> = bindSync {
        !unmarshalResultHelper<AccountChangesCore>(rawResult)
    }

    override fun produceResult(message: AccountChangesCore): Either<PbkException, Unit> = bindSync {
        logger.info("Received account changes for entity [${message.entityId}]")
        !accountProducer.recordVersionChangeAccount(message.toAccountChange())
        if (dcsAccountTerminationProcessingEnabled) {
            !terminationAccountProducer.recordVersionChangeAccount(message.toAccountChange())
        }
    }
}