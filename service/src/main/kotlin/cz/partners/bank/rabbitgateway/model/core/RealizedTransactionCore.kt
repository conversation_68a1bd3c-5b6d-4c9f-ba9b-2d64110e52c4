package cz.partners.bank.rabbitgateway.model.core

import arrow.core.Either
import cz.partners.bank.common.api.contract.AmountIsoCurrency
import cz.partners.bank.common.api.contract.CurrencyIsoCode
import cz.partners.bank.common.api.contract.PaymentCodes
import cz.partners.bank.core.api.BankAccountIdentification
import cz.partners.bank.core.api.ChannelCode
import cz.partners.bank.core.api.TransactionTypeCode
import cz.partners.bank.core.api.TransferPropertyCode
import cz.partners.bank.rabbitgateway.exception.MappingException
import cz.partners.bank.rabbitgateway.helper.CoreTransformHelper
import cz.partners.bank.rabbitgateway.helper.CoreTransformHelper.transformYesNoToBoolean
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.RealizedTransactionMessage
import cz.pbktechnology.platform.common.helper.bindSync
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

data class RealizedTransactionCore(
    val accountNameCredit: String?,
    val accountNameDebit: String?,
    val accountNumberCredit: String,
    val accountNumberDebit: String,
    val actionTypeCode: String,
    val availableBalanceCredit: BigDecimal?,
    val availableGpeBalanceCredit: BigDecimal?,
    val accountBalanceCredit: BigDecimal?,
    val accountBalanceDebit: BigDecimal?,
    val technicalAccountNumberCredit: String,
    val technicalIdAccountCredit: String,
    val availableBalanceDebit: BigDecimal?,
    val availableGpeBalanceDebit: BigDecimal?,
    val technicalAccountNumberDebit: String,
    val technicalIdAccountDebit: String,
    val amount: BigDecimal,
    val originalAmount: BigDecimal?,
    val originalCurrencyRate: BigDecimal?,
    val originalCurrencyCode: CurrencyIsoCode?,
    val bankCodeCredit: String,
    val bankCodeDebit: String,
    val channelCode: ChannelCode?,
    val constSymbol: String?,
    val createdAt: OffsetDateTime,
    val currencyCode: CurrencyIsoCode,
    val entity: Any?,
    val entityId: Long?,
    val errorMessage: String? = null,
    val eventTimestamp: OffsetDateTime,
    val externalId: String?,
    val rowNumber: String?,
    val idAccountCredit: Long,
    val idAccountDebit: Long,
    val idPaymentCard: Long?, // TODO neposilaji
    val idPaymentTerminal: String?,
    val idRealizedTransaction: Long,
    val idRepeatedTransaction: Long?, // TODO neposilaji
    val idRequiredTransaction: Long,
    val idTransactionChain: Long,
    val idCancelledTransaction: Long?,
    val merchantId: String?,
    val merchAbrName: String?,
    val merchCity: String?,
    val merchCountry: String?,
    val realizationDate: LocalDate,
    val specSymbol: String?,
    val textMessageCredit: String?,
    val transactionTypeCode: TransactionTypeCode,
    val transferProperty: TransferPropertyCode,
    val mainTransaction: String, // TODO nemapujeme
    val valutaDate: LocalDate?,
    val varSymbol: String?,
    val ecbDeviation: BigDecimal?,
    val transmissionDateTime: String?,
    val terminalId: String?,
    val cancelationReason: String?,
    val mccCode: String?,
    val pointCode: String?,
    val walletId: String?,
    val token: String?,
    val tokenReqId: String?,
    val uniqueExternalId: String?,
    val acqRefNr: String?,
    val requiredAmount: BigDecimal?,
    val requiredCurrencyCode: CurrencyIsoCode?,
    val paymentPurpose : String?,
    val end2endreference: String?,
    val usedCurrRate: BigDecimal?,
    val acquirerCmi: Long?,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    fun toMessage(): Either<MappingException, RealizedTransactionMessage> = bindSync {
        RealizedTransactionMessage(
            creditAccountIdentification = BankAccountIdentification(
                accountId = idAccountCredit,
                accountNumber = accountNumberCredit,
                accountName = accountNameCredit,
                bankCode = bankCodeCredit,
                isTechnical = transformYesNoToBoolean(technicalAccountNumberCredit),
                isInternal = !transformYesNoToBoolean(technicalIdAccountCredit),
            ),
            creditBalances = getCreditBalances(),
            debitAccountIdentification = BankAccountIdentification(
                accountId = idAccountDebit,
                accountNumber = accountNumberDebit,
                accountName = accountNameDebit,
                bankCode = bankCodeDebit,
                isTechnical = transformYesNoToBoolean(technicalAccountNumberDebit),
                isInternal = !transformYesNoToBoolean(technicalIdAccountDebit),
            ),
            debitBalances = getDebitBalances(),
            paymentCodes = PaymentCodes(
                variable = varSymbol,
                constant = constSymbol,
                specific = specSymbol,
            ),
            amount = AmountIsoCurrency(
                value = amount,
                currencyCode = currencyCode,
            ),
            originalAmount = CoreTransformHelper.getOriginalAmount(this@RealizedTransactionCore),
            createdAt = createdAt,
            externalId = externalId,
            rowNumber = rowNumber,
            errorMessage = errorMessage,
            eventTimestamp = eventTimestamp,
            channelCode = if (channelCode == ChannelCode.PAYMENTCARD_SETLEMENT) ChannelCode.PAYMENTCARD_SETTLEMENT else channelCode,
            merchantId = merchantId,
            merchantAbrName = merchAbrName,
            merchantCity = merchCity,
            merchantCountry = merchCountry,
            paymentCardId = idPaymentCard,
            repeatedTransactionId = idRepeatedTransaction,
            requiredTransactionId = idRequiredTransaction,
            realizedTransactionId = idRealizedTransaction,
            canceledTransactionId = idCancelledTransaction,
            transactionChainId = idTransactionChain,
            realizationDate = realizationDate,
            textMessageCredit = textMessageCredit,
            transactionTypeCode = transactionTypeCode,
            transferPropertyCode = transferProperty,
            validFrom = OffsetDateTime.now(),
            validTo = OffsetDateTime.now(),
            valutaDate = valutaDate,
            actionTypeCode = actionTypeCode,
            ecbDeviation = ecbDeviation,
            transmissionDateTime = transmissionDateTime ?.let { OffsetDateTime.parse(it + "Z", DateTimeFormatter.ISO_OFFSET_DATE_TIME) },
            terminalId = terminalId,
            cancellationReason = mapCancellationReason(
                id = idRealizedTransaction,
                cancellationReason = cancelationReason,
                logger = logger
            ),
            coreEntityType = mapCoreTransactionEntityType(entity),
            coreEntityId = entityId,
            mccCode = mccCode,
            pointCode = pointCode,
            walletId = walletId,
            token = !token.isNullOrEmpty(),
            tokenReqId = tokenReqId,
            uniqueExternalId = uniqueExternalId,
            acqRefNr = acqRefNr,
            requiredAmount = requiredAmount?.let { value ->
                requiredCurrencyCode?.let { currencyCode ->
                    AmountIsoCurrency(value, currencyCode)
                }
            },
            paymentPurpose = paymentPurpose,
            end2endreference = end2endreference,
            usedCurrRate = usedCurrRate,
            acquirerId = acquirerCmi,
        )
    }

    private fun getCreditBalances(): Balances? {
        return if (availableBalanceCredit != null && availableGpeBalanceCredit != null && accountBalanceCredit != null) {
            Balances(
                date = eventTimestamp,
                amountAvailableBalance = availableBalanceCredit,
                amountAvailableBalanceWithoutCardHolds = availableGpeBalanceCredit,
                amountAccountBalance = accountBalanceCredit,
                currencyCode = "CZK" //TODO: get it from DetailIT
            )
        } else {
            null
        }
    }

    private fun getDebitBalances(): Balances? {
        return if (availableBalanceDebit != null && availableGpeBalanceDebit != null && accountBalanceDebit != null) {
            Balances(
                date = eventTimestamp,
                amountAvailableBalance = availableBalanceDebit,
                amountAvailableBalanceWithoutCardHolds = availableGpeBalanceDebit,
                amountAccountBalance = accountBalanceDebit,
                currencyCode = "CZK" //TODO: get it from DetailIT
            )
        } else {
            null
        }
    }

}

