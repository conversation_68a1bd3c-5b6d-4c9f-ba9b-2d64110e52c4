package cz.partners.bank.rabbitgateway.model.core

import arrow.core.Either
import cz.partners.bank.common.api.contract.AmountIsoCurrency
import cz.partners.bank.common.api.contract.CurrencyIsoCode
import cz.partners.bank.core.api.BankAccountIdentification
import cz.partners.bank.core.api.ChannelCode
import cz.partners.bank.core.api.TransactionTypeCode
import cz.partners.bank.rabbitgateway.exception.MappingException
import cz.partners.bank.rabbitgateway.helper.CoreTransformHelper.getOriginalAmount
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.RealizedHoldMessage
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class RealizedHoldCore(
    val idRealizedHold: Long, //1000206
    val idRequiredTransaction: Long, //1001018
    val idTransactionChain: Long, //1001018
    val externalId: String?, // "000301400620300242749ROWN009526299231"
    val amount: BigDecimal,
    val currencyCode: CurrencyIsoCode, //"CZK"
    val channelCode: ChannelCode, //"SYSTEM"
    val actionTypeCode: String, //"CARD_WITHDRAWAL"
    val transactionTypeCode: TransactionTypeCode, //"HOLD"
    val valutaDate: LocalDate, //"2021-11-10T00:00:00"
    val realizationDate: LocalDate, //"2021-11-10T00:00:00"
    val idAccount: Long, //1000221
    val accountNumber: String, //"**********",
    val accountName: String?, //"Jára Cimmermann",
    val availableBalance: BigDecimal,
    val availableGpeBalance: BigDecimal,
    val accountBalance: BigDecimal,
    val textMessage: String?, //null,
    val merchantId: String?, //"6011"
    val ecbDeviation: BigDecimal?, // 15.36,
    val idPaymentCard: Long, //1000020
    val usedInvCurrRate: BigDecimal?, //28.0675
    val usedCurrRate: BigDecimal?,  // 28.0675
    val originalAmount: BigDecimal?,
    val originalCurrencyRate: BigDecimal?,
    val originalCurrencyCode: CurrencyIsoCode?,
    val requiredCurrencyCode: CurrencyIsoCode?, //"CZK"
    val requiredAmount: BigDecimal?, // 1
    val createdAt: OffsetDateTime, // "2021-11-10T15:30:00.*********+00:00"
    val eventTimestamp: OffsetDateTime, //"2021-11-10T15:30:00.*********+00:00"
    val cancelationReason: String?, //"EXPIRED"
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun toMessage(): Either<MappingException, RealizedHoldMessage> {
        return Either.catch {
            RealizedHoldMessage(
                accountIdentification = BankAccountIdentification(
                    accountId = idAccount,
                    accountNumber = accountNumber,
                    accountName = accountName,
                    bankCode = PARTNERS_BANK_CODE,
                    isTechnical = false,
                    isInternal = true
                ),
                balances = Balances(
                    date = eventTimestamp,
                    amountAvailableBalance = availableBalance,
                    amountAvailableBalanceWithoutCardHolds = availableGpeBalance,
                    amountAccountBalance = accountBalance,
                    currencyCode = "CZK" //TODO: get it from DetailIT
                ),
                amount = AmountIsoCurrency(
                    value = amount,
                    currencyCode = currencyCode,
                ),
                originalAmount = getOriginalAmount(this),
                createdAt = createdAt,
                externalId = externalId,
                eventTimestamp = eventTimestamp,
                channelCode = if (channelCode == ChannelCode.PAYMENTCARD_SETLEMENT) ChannelCode.PAYMENTCARD_SETTLEMENT else channelCode,
                merchantId = merchantId,
                paymentCardId = idPaymentCard,
                requiredTransactionId = idRequiredTransaction,
                transactionChainId = idTransactionChain,
                realizedHoldId = idRealizedHold,
                realizationDate = realizationDate,
                textMessage = textMessage,
                transactionTypeCode = transactionTypeCode,
                valutaDate = valutaDate,
                actionTypeCode = actionTypeCode,
                ecbDeviation = ecbDeviation,
                cancellationReason = mapCancellationReason(
                    id = idRealizedHold,
                    cancellationReason = cancelationReason,
                    logger = logger
                ),
            )
        }.mapLeft { MappingException("Unable to map core card hold message", it) }
    }

    companion object {
        const val PARTNERS_BANK_CODE = "6363"
    }
}

