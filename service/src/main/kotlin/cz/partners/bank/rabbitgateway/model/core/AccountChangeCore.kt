package cz.partners.bank.rabbitgateway.model.core

import cz.partners.bank.rabbitgateway.rabbitapi.AccountVersionChangeMessage
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID

data class AccountChangesCore(
    val entity: String,
    val entityId: Long,
    val recordVersion: Long,
    val status: String,
    val wfStatus: String,
    val requestId: UUID?,
    val processId: UUID?,
    val targetAccounts: List<TargetAccount>?,
    val settlementAccounts: List<SettlementAccount>?,
    val closingOrder: String?,
    val reason: String?,
    val priority: String?,
    val eventTimestamp: String
) {
    fun toAccountChange() = AccountVersionChangeMessage(
        coreId = entityId,
        recordVersion = recordVersion.toInt(),
        eventTimestamp = OffsetDateTime.parse(eventTimestamp),
        status = status,
        totalSettlementAmount = settlementAccounts?.sumOf(SettlementAccount::settlementAmount),
    )
}

data class TargetAccount(
    val targetAccountNumber: String,
    val targetBankCode: String,
    val percentValue: Int,
    val orderNo: Int
)

data class SettlementAccount(
    val settlementAccountNumber: String,
    val settlementBankCode: String,
    val settlementAction: String,
    val settlementAmount: BigDecimal
)
