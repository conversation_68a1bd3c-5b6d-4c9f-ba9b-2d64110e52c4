package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.model.core.RequiredTransactionCore
import cz.partners.bank.rabbitgateway.producer.RequiredIncomingProducer
import cz.partners.bank.rabbitgateway.producer.TransactionProducer
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.RequiredTransactionMessage
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bindSync
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
open class MessageRelayRequiredServiceImpl(
    private val transactionProducer: TransactionProducer,
    oracleDataSource: OracleDataSource,
    failedProducer: RequiredIncomingProducer,
    objectMapper: ObjectMapper,
    oracleAqJmsService: OracleAqJmsService
) : MessageRelayCommonServiceImpl<RequiredTransactionMessage>(
    failedProducer,
    objectMapper,
    oracleDataSource,
    oracleAqJmsService
) {
    override val logger = LoggerFactory.getLogger(this::class.java)
    
    override val queueName: String = REQUIRED_TRANSACTION_CORE_QUEUE

    override fun dequeueMessage(
        instance: String,
        timeoutSeconds: Int,
    ): Either<PbkException, String> = oracleDataSource.dequeueMessage(
        queueName = REQUIRED_TRANSACTION_CORE_QUEUE,
        instance = instance,
        timeoutSeconds = timeoutSeconds
    )

    override fun unmarshalResult(
        rawResult: String,
    ): Either<QueueParseException, RequiredTransactionMessage> = bindSync {
        val unmarshalledMessage = !unmarshalResultHelper<RequiredTransactionCore>(rawResult)
        !unmarshalledMessage.toMessage()
            .mapLeft { exception ->
                QueueParseException(
                    rawResult,
                    exception
                )
            }
    }

    override fun produceResult(message: RequiredTransactionMessage): Either<PbkException, Unit> {
        logger.info("Received required transaction [${message.requiredTransactionId}]")
        return transactionProducer.requiredTransaction(message)
    }

    companion object {
        const val REQUIRED_TRANSACTION_CORE_QUEUE = "REQUIRED_TRANSACTION"
    }
}
