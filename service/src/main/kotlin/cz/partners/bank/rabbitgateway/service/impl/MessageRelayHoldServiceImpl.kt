package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import cz.partners.bank.rabbitgateway.configuration.LooperIndicator
import cz.partners.bank.rabbitgateway.configuration.OracleAqConfiguration
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.model.core.RealizedHoldCore
import cz.partners.bank.rabbitgateway.producer.RealizedHoldIncomingProducer
import cz.partners.bank.rabbitgateway.producer.TransactionProducer
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.RealizedHoldMessage
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.partners.bank.rabbitgateway.service.RelayLooper
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bindSync
import io.micronaut.context.annotation.Parameter
import jakarta.inject.Named
import jakarta.inject.Singleton
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Singleton
open class MessageRelayHoldServiceImpl(
    private val transactionProducer: TransactionProducer,
    failedProducer: RealizedHoldIncomingProducer,
    objectMapper: ObjectMapper,
    oracleDataSource: OracleDataSource,
    oracleAqJmsService: OracleAqJmsService,
    oracleAqConfig: OracleAqConfiguration
) : MessageRelayCommonServiceImpl<RealizedHoldMessage>(
    failedProducer,
    objectMapper,
    oracleDataSource,
    oracleAqJmsService,
    oracleAqConfig
) {
    override val logger: Logger = LoggerFactory.getLogger(this::class.java)
    
    override val queueName: String = REALIZED_HOLD_CORE_QUEUE

    override fun dequeueMessage(
        instance: String,
        timeoutSeconds: Int,
    ): Either<PbkException, String> = oracleDataSource.dequeueMessage(
        queueName = REALIZED_HOLD_CORE_QUEUE,
        instance = instance,
        timeoutSeconds = timeoutSeconds
    )

    override fun unmarshalResult(rawResult: String): Either<QueueParseException, RealizedHoldMessage> = bindSync {
        val unmarshalledMessage = !unmarshalResultHelper<RealizedHoldCore>(rawResult)
        !unmarshalledMessage.toMessage()
            .mapLeft { exception ->
                QueueParseException(
                    rawResult,
                    exception
                )
            }
    }

    override fun produceResult(message: RealizedHoldMessage): Either<PbkException, Unit> {
        logger.info("Received realized transaction [${message.realizedHoldId}]")
        return transactionProducer.cardHold(message)
    }

    companion object {
        const val REALIZED_HOLD_CORE_QUEUE = "REALIZED_HOLD"
    }
}
