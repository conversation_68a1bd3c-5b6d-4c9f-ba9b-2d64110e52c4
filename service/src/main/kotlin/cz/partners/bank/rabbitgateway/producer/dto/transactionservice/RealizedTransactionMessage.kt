package cz.partners.bank.rabbitgateway.producer.dto.transactionservice

import cz.partners.bank.common.api.contract.AmountIsoCurrency
import cz.partners.bank.common.api.contract.PaymentCodes
import cz.partners.bank.core.api.BankAccountIdentification
import cz.partners.bank.core.api.ChannelCode
import cz.partners.bank.core.api.OriginalAmount
import cz.partners.bank.core.api.TransactionTypeCode
import cz.partners.bank.core.api.TransferPropertyCode
import cz.partners.bank.rabbitgateway.model.core.Balances
import cz.partners.bank.rabbitgateway.model.core.CancelHoldReason
import cz.partners.bank.rabbitgateway.util.maskAccountNumber
import cz.partners.bank.rabbitgateway.util.maskMessage
import cz.partners.bank.rabbitgateway.util.maskName
import cz.partners.bank.rabbitgateway.util.maskSensitive
import cz.partners.bank.rabbitgateway.util.maskSensitiveText
import io.micronaut.core.annotation.Introspected
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

@Introspected
data class RealizedTransactionMessage(
    val creditAccountIdentification: BankAccountIdentification,
    val creditBalances: Balances?,
    val debitAccountIdentification: BankAccountIdentification,
    val debitBalances: Balances?,
    val amount: AmountIsoCurrency,
    val originalAmount: OriginalAmount?,
    val externalId: String?,
    val rowNumber: String?,
    val paymentCodes: PaymentCodes,
    val createdAt: OffsetDateTime,
    val errorMessage: String?,
    val eventTimestamp: OffsetDateTime,
    val channelCode: ChannelCode?,
    val merchantId: String?,
    val merchantAbrName: String?,
    val merchantCity: String?,
    val merchantCountry: String?,
    val paymentCardId: Long?,
    val repeatedTransactionId: Long?,
    val requiredTransactionId: Long,
    val realizedTransactionId: Long,
    val canceledTransactionId: Long?,
    val transactionChainId: Long,
    val realizationDate: LocalDate,
    val textMessageCredit: String?,
    val transactionTypeCode: TransactionTypeCode,
    val transferPropertyCode: TransferPropertyCode,
    val validFrom: OffsetDateTime,
    val validTo: OffsetDateTime,
    val valutaDate: LocalDate?,
    val actionTypeCode: String,
    val ecbDeviation: BigDecimal?,
    val transmissionDateTime: OffsetDateTime?,
    val terminalId: String?,
    val cancellationReason: CancelHoldReason?,
    val coreEntityType: TransactionEntityType?,
    val coreEntityId: Long?,
    val mccCode: String?,
    val pointCode: String?,
    val walletId: String?,
    val token: Boolean?,
    val tokenReqId: String?,
    val uniqueExternalId: String?,
    val acqRefNr: String?,
    val requiredAmount: AmountIsoCurrency?,
    val paymentPurpose: String?,
    val end2endreference: String?,
    val usedCurrRate: BigDecimal?,
    val acquirerId: Long?,
) {
    override fun toString(): String {
        return "RealizedTransactionMessage(" +
                "creditAccountIdentification=BankAccountIdentification(" +
                "accountId=${creditAccountIdentification.accountId}, " +
                "accountNumber=${creditAccountIdentification.accountNumber.maskAccountNumber()}, " +
                "accountName=${creditAccountIdentification.accountName.maskName()}, " +
                "bankCode=${creditAccountIdentification.bankCode}, " +
                "isTechnical=${creditAccountIdentification.isTechnical}, " +
                "isInternal=${creditAccountIdentification.isInternal}" +
                "), " +
                "creditBalances=Balances(" +
                "date=${creditBalances?.date}, " +
                "amountAvailableBalance=${creditBalances?.amountAvailableBalance?.maskSensitive()}, " +
                "amountAvailableBalanceWithoutCardHolds=${creditBalances?.amountAvailableBalanceWithoutCardHolds?.maskSensitive()}, " +
                "currencyCode=${creditBalances?.currencyCode}" +
                "), " +
                "debitAccountIdentification=BankAccountIdentification(" +
                "accountId=${debitAccountIdentification.accountId}, " +
                "accountNumber=${debitAccountIdentification.accountNumber.maskAccountNumber()}, " +
                "accountName=${debitAccountIdentification.accountName.maskName()}, " +
                "bankCode=${debitAccountIdentification.bankCode}, " +
                "isTechnical=${debitAccountIdentification.isTechnical}, " +
                "isInternal=${debitAccountIdentification.isInternal}" +
                "), " +
                "debitBalances=Balances(" +
                "date=${debitBalances?.date}, " +
                "amountAvailableBalance=${debitBalances?.amountAvailableBalance?.maskSensitive()}, " +
                "amountAvailableBalanceWithoutCardHolds=${debitBalances?.amountAvailableBalanceWithoutCardHolds?.maskSensitive()}, " +
                "currencyCode=${debitBalances?.currencyCode}" +
                "), " +
                "amount=AmountIsoCurrency(" +
                "value=${amount.value.maskSensitive()}, " +
                "currencyCode=${amount.currencyCode}" +
                "), " +
                "originalAmount=OriginalAmount(" +
                "value=${originalAmount?.value?.maskSensitive()}, " +
                "currencyCode=${originalAmount?.currencyCode}, " +
                "rate=${originalAmount?.rate}" +
            "), " +
            "externalId=$externalId, " +
            "rowNumber=$rowNumber, " +
            "paymentCodes=$paymentCodes, " +
            "createdAt=$createdAt, " +
            "errorMessage=$errorMessage, " +
            "eventTimestamp=$eventTimestamp, " +
            "channelCode=$channelCode, " +
            "merchantId=$merchantId, " +
            "merchantAbrName=$merchantAbrName, " +
            "merchantCity=$merchantCity, " +
            "merchantCountry=$merchantCountry, " +
            "paymentCardId=$paymentCardId, " +
            "repeatedTransactionId=$repeatedTransactionId, " +
            "requiredTransactionId=$requiredTransactionId, " +
            "realizedTransactionId=$realizedTransactionId, " +
            "canceledTransactionId=$canceledTransactionId, " +
            "transactionChainId=$transactionChainId, " +
            "realizationDate=$realizationDate, " +
            "textMessageCredit=${textMessageCredit.maskMessage()}, " +
            "transactionTypeCode=$transactionTypeCode, " +
            "transferPropertyCode=$transferPropertyCode, " +
            "validFrom=$validFrom, " +
            "validTo=$validTo, " +
            "valutaDate=$valutaDate, " +
            "actionTypeCode=$actionTypeCode, " +
            "ecbDeviation=$ecbDeviation, " +
            "transmissionDateTime=$transmissionDateTime, " +
            "terminalId=$terminalId, " +
            "cancellationReason=$cancellationReason, " +
            "coreEntityType=$coreEntityType" +
            "coreEntityId=$coreEntityId, " +
            "mccCode=$mccCode, " +
            "pointCode=$pointCode, " +
            "walletId=$walletId, " +
            "token=$token, " +
            "tokenReqId=$tokenReqId, " +
            "uniqueExternalId=$uniqueExternalId, " +
            "acqRefNr=$acqRefNr," +
            "requiredAmount=AmountIsoCurrency(" +
                "value=${requiredAmount?.value?.maskSensitive()}, " +
                "currencyCode=${requiredAmount?.currencyCode}" +
                ") " +
                "acqRefNr=$acqRefNr" +
                "paymentPurpose=${paymentPurpose.maskSensitiveText()}" +
                "end2endreference=$end2endreference" +
                "usedCurrRate=$usedCurrRate" +
                "acquirerId=$acquirerId" +
                ")"
    }
}
