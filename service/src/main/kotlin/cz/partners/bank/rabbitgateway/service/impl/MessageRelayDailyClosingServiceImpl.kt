package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.databind.ObjectMapper
import cz.partners.bank.rabbitgateway.configuration.OracleAqConfiguration
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.model.core.DailyClosingCore
import cz.partners.bank.rabbitgateway.producer.DailyClosingIncomingProducer
import cz.partners.bank.rabbitgateway.producer.StandingOrderProducer
import cz.partners.bank.rabbitgateway.rabbitapi.DailyClosingType
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bindSync
import jakarta.inject.Singleton
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Singleton
open class MessageRelayDailyClosingServiceImpl(
    private val standingOrderProducer: StandingOrderProducer,
    incomingProducer: DailyClosingIncomingProducer,
    objectMapper: ObjectMapper,
    oracleDataSource: OracleDataSource,
    oracleAqJmsService: OracleAqJmsService,
    oracleAqConfig: OracleAqConfiguration
) : MessageRelayCommonServiceImpl<DailyClosingCore>(
    incomingProducer,
    objectMapper,
    oracleDataSource,
    oracleAqJmsService,
    oracleAqConfig
) {
    override val logger: Logger = LoggerFactory.getLogger(this::class.java)
    
    override val queueName: String = DAILY_CLOSING_CORE_QUEUE

    override fun dequeueMessage(
        instance: String,
        timeoutSeconds: Int,
    ): Either<PbkException, String> = oracleDataSource.dequeueMessage(
        queueName = DAILY_CLOSING_CORE_QUEUE,
        instance = instance,
        timeoutSeconds = timeoutSeconds
    )

    override fun unmarshalResult(rawResult: String): Either<QueueParseException, DailyClosingCore> = bindSync {
        !unmarshalResultHelper<DailyClosingCore>(rawResult)
    }

    override fun produceResult(message: DailyClosingCore): Either<PbkException, Unit> {
        logger.info("Received daily closing message [$message]")

        val eodMessage = message.toMessage(message.type)

        return when (message.type) {
            DailyClosingType.EVENING_END -> {
                standingOrderProducer.publish(eodMessage)
            }

            DailyClosingType.MORNING_END -> {
                standingOrderProducer.publish(eodMessage)
            }

            else -> logger.debug("Daily closing ignored for type[${message.type}]")
        }.right()
    }

    companion object {
        const val DAILY_CLOSING_CORE_QUEUE = "DAILY_CLOSING"
    }
}
