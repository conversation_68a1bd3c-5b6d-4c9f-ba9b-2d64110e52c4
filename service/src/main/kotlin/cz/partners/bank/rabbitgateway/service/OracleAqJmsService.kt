package cz.partners.bank.rabbitgateway.service

import cz.partners.bank.rabbitgateway.configuration.OracleAqJmsConfiguration
import jakarta.annotation.PreDestroy
import jakarta.inject.Singleton
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlin.coroutines.coroutineContext
import oracle.jms.AQjmsSession
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import javax.jms.Message
import javax.jms.MessageConsumer
import javax.jms.QueueConnection
import javax.jms.Session
import javax.jms.JMSException

/**
 * Service for reactive Oracle AQ JMS integration
 */
@Singleton
open class OracleAqJmsService(
    private val jmsConnection: QueueConnection,
    private val oracleAqJmsConfiguration: OracleAqJmsConfiguration,
    private val contextManager: JmsContextManager,
    private val retryPolicy: RetryPolicy
) {
    private val logger: Logger = LoggerFactory.getLogger(OracleAqJmsService::class.java)
    
    @PreDestroy
    fun cleanup() {
        try {
            logger.info("Closing Oracle AQ JMS connection")
            jmsConnection.close()
        } catch (e: Exception) {
            logger.warn("Error closing Oracle AQ JMS connection during shutdown", e)
        }
    }

    /**
     * Starts listening to messages on the specified Oracle AQ queue
     * This should be called from within a coroutine context
     *
     * @param queueName The name of the Oracle AQ queue
     * @param instance Instance identifier for logging
     * @param processMessage Callback to process each message, returns result that determines transaction outcome
     */
    suspend fun listenToQueue(
        queueName: String,
        instance: String,
        processMessage: (Message) -> MessageProcessingResult
    ) {
        val capturedContext = contextManager.captureCurrentContext()

        retryPolicy.executeWithRetry(instance) {
            startQueueListener(queueName, instance, capturedContext, processMessage)
        }
    }
    
    /**
     * Internal method to set up and run the queue listener
     */
    private suspend fun startQueueListener(
        queueName: String,
        instance: String,
        capturedContext: JmsContextManager.CapturedContext,
        processMessage: (Message) -> MessageProcessingResult
    ) {
        // Use Kotlin's 'use' extension for automatic resource management
        jmsConnection.createQueueSession(true, Session.SESSION_TRANSACTED).use { session ->
            val aqSession = session as AQjmsSession

            // Get queue & create consumer with schema
            logger.debug("Using Oracle AQ schema: ${oracleAqJmsConfiguration.oracleAqSchema()} for queue: $queueName")
            val queue = aqSession.getQueue(oracleAqJmsConfiguration.oracleAqSchema(), queueName)

            aqSession.createConsumer(queue).use { consumer ->
                // Set up message listener with context propagation
                consumer.setMessageListener { message ->
                    contextManager.withContext(capturedContext) {
                        handleMessage(message, instance, aqSession, processMessage)
                    }
                }

                logger.info("[$instance] Started listening to queue [$queueName]")

                // Keep the coroutine alive while listening
                while (coroutineContext.isActive) {
                    delay(5000) // Check every 5 seconds if we should keep running
                }
            }
        }
    }

    /**
     * Handles individual message processing with proper transaction management
     */
    private fun handleMessage(
        message: Message,
        instance: String,
        session: AQjmsSession,
        processMessage: (Message) -> MessageProcessingResult
    ) {
        logger.debug("[$instance] Received AQ JMS message: ${message.jmsMessageID}")

        try {
            val processingResult = processMessage(message)

            // Handle transaction based on processing result
            when (processingResult) {
                is MessageProcessingResult.Success -> {
                    logger.debug("[$instance] Message processed successfully, committing transaction")
                    session.commit()
                }
                is MessageProcessingResult.PoisonMessage -> {
                    logger.warn("[$instance] Poison message detected: ${processingResult.reason}, committing to discard")
                    session.commit()
                }
                is MessageProcessingResult.Failure -> {
                    logger.error("[$instance] Message processing failed, rolling back transaction", processingResult.error)
                    session.rollback()

                    // If it's a JMS error, re-throw to trigger consumer restart
                    if (processingResult.error is JMSException) {
                        logger.error("[$instance] JMS error detected, consumer will restart", processingResult.error)
                        throw processingResult.error
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("[$instance] Unexpected error in message processing", e)
            try {
                session.rollback()
            } catch (rollbackEx: JMSException) {
                logger.error("[$instance] CRITICAL: Failed to rollback after unexpected error", rollbackEx)
            }

            // Re-throw JMS exceptions to trigger consumer restart
            if (e is JMSException) {
                throw e
            }
        }
    }
}

/**
 * Extension function to provide 'use' semantics for JMS Session
 */
private inline fun <T> Session.use(block: (Session) -> T): T {
    try {
        return block(this)
    } finally {
        try {
            this.close()
        } catch (e: Exception) {
            // Log but don't throw to avoid masking original exception
            LoggerFactory.getLogger("JMS").warn("Error closing JMS session", e)
        }
    }
}

/**
 * Extension function to provide 'use' semantics for JMS MessageConsumer
 */
private inline fun <T> MessageConsumer.use(block: (MessageConsumer) -> T): T {
    try {
        return block(this)
    } finally {
        try {
            this.close()
        } catch (e: Exception) {
            // Log but don't throw to avoid masking original exception
            LoggerFactory.getLogger("JMS").warn("Error closing JMS consumer", e)
        }
    }
}
