package cz.partners.bank.rabbitgateway.service

import cz.partners.bank.rabbitgateway.configuration.OracleAqJmsConfiguration
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.ContextProvider
import cz.pbktechnology.platform.common.context.OperationContext
import jakarta.annotation.PreDestroy
import jakarta.inject.Singleton
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlin.coroutines.coroutineContext
import kotlin.math.min
import kotlin.math.pow
import oracle.jms.AQjmsSession
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import java.util.UUID
import javax.jms.Message
import javax.jms.MessageConsumer
import javax.jms.QueueConnection
import javax.jms.Session
import javax.jms.JMSException

/**
 * Service for reactive Oracle AQ JMS integration
 */
@Singleton
open class OracleAqJmsService(
    private val jmsConnection: QueueConnection,
    private val oracleAqJmsConfiguration: OracleAqJmsConfiguration,
    private val contextProvider: ContextProvider
) {
    private val logger: Logger = LoggerFactory.getLogger(OracleAqJmsService::class.java)
    
    @PreDestroy
    fun cleanup() {
        try {
            logger.info("Closing Oracle AQ JMS connection")
            jmsConnection.close()
        } catch (e: Exception) {
            logger.warn("Error closing Oracle AQ JMS connection during shutdown", e)
        }
    }

    /**
     * Starts listening to messages on the specified Oracle AQ queue
     * This should be called from within a coroutine context
     *
     * @param queueName The name of the Oracle AQ queue
     * @param instance Instance identifier for logging
     * @param processMessage Callback to process each message, returns result that determines transaction outcome
     */
    suspend fun listenToQueue(
        queueName: String, 
        instance: String,
        processMessage: (Message) -> MessageProcessingResult
    ) {
        // Set up proper context for each message processing
        contextProvider.withJobContext(instance) {
            var attempt = 0
            while (coroutineContext.isActive) {
                try {
                    startQueueListener(queueName, instance, processMessage)
                } catch (e: CancellationException) {
                    logger.info("[$instance] Queue listener cancelled for [$queueName]")
                    throw e
                } catch (e: Exception) {
                    attempt++
                    val backoffDelay = min(1000 * (2.0.pow(attempt)).toLong(), 60000) // Max 60 seconds
                    logger.warn("[$instance] Error in queue listener for [$queueName], retrying in ${backoffDelay}ms (attempt $attempt)", e)
                    delay(backoffDelay)
                }
            }
        }
    }
    
    /**
     * Internal method to set up and run the queue listener
     */
    private suspend fun startQueueListener(
        queueName: String,
        instance: String,
        processMessage: (Message) -> MessageProcessingResult
    ) {
        var jmsSession: AQjmsSession? = null
        var consumer: MessageConsumer? = null
        
        // Capture all context elements from the parent coroutine
        val parentOperationContext = ContextConfiguration.operationContext.get()
        val parentMDCContext = MDC.getCopyOfContextMap() ?: emptyMap()
        val parentOTelContext = ContextConfiguration.openTelemetryThreadLocalContext.get()
        
        try {
            jmsSession = jmsConnection.createQueueSession(true, Session.SESSION_TRANSACTED) as AQjmsSession
            
            // Get queue & create consumer with schema
            logger.debug("Using Oracle AQ schema: ${oracleAqJmsConfiguration.oracleAqSchema()} for queue: $queueName")
            val queue = jmsSession.getQueue(oracleAqJmsConfiguration.oracleAqSchema(), queueName)
            consumer = jmsSession.createConsumer(queue)
            
            val currentSession = jmsSession
            
            // Set up message listener
            consumer.setMessageListener { message ->
                logger.debug("[$instance] Received AQ JMS message from [$queueName]: ${message.jmsMessageID}")

                try {
                    // Restore all context elements on JMS thread
                    // 1. Set OperationContext
                    val contextToUse = parentOperationContext ?: OperationContext(UUID.randomUUID())
                    ContextConfiguration.operationContext.set(contextToUse)
                    
                    // 2. Set MDC context
                    if (parentMDCContext.isNotEmpty()) {
                        MDC.setContextMap(parentMDCContext)
                    }
                    
                    // 3. Set OpenTelemetry context
                    if (parentOTelContext != null) {
                        ContextConfiguration.openTelemetryThreadLocalContext.set(parentOTelContext)
                        // Note: We can't call makeCurrent() without proper OpenTelemetry dependency
                        // This still ensures the ThreadLocal is set for any code that needs it
                    }
                    
                    val processingResult = processMessage(message)

                    // Handle transaction based on processing result
                    when (processingResult) {
                        is MessageProcessingResult.Success -> {
                            logger.debug("[$instance] Message processed successfully, committing transaction")
                            currentSession.commit()
                        }
                        is MessageProcessingResult.PoisonMessage -> {
                            logger.warn("[$instance] Poison message detected: ${processingResult.reason}, committing to discard")
                            currentSession.commit()
                        }
                        is MessageProcessingResult.Failure -> {
                            logger.error("[$instance] Message processing failed, rolling back transaction", processingResult.error)
                            currentSession.rollback()

                            // If it's a JMS error, re-throw to trigger consumer restart
                            if (processingResult.error is JMSException) {
                                logger.error("[$instance] JMS error detected, consumer will restart", processingResult.error)
                                throw processingResult.error
                            }
                        }
                    }
                } catch (e: Exception) {
                    logger.error("[$instance] Unexpected error in message listener for queue [$queueName]", e)
                    try {
                        currentSession.rollback()
                    } catch (rollbackEx: JMSException) {
                        logger.error("[$instance] CRITICAL: Failed to rollback after unexpected error", rollbackEx)
                    }

                    // Re-throw JMS exceptions to trigger consumer restart
                    if (e is JMSException) {
                        throw e
                    }
                } finally {
                    // Clean up all context elements from JMS thread
                    ContextConfiguration.operationContext.remove()
                    ContextConfiguration.openTelemetryThreadLocalContext.remove()
                    MDC.clear()
                }
            }
            
            logger.info("[$instance] Started listening to queue [$queueName]")
            
            // Keep the coroutine alive while listening
            while (coroutineContext.isActive) {
                delay(5000) // Check every 5 seconds if we should keep running
            }
            
        } finally {
            // Clean up resources
            try {
                consumer?.close()
                jmsSession?.close()
                logger.debug("[$instance] Closed resources for queue [$queueName]")
            } catch (e: Exception) {
                logger.warn("[$instance] Error closing AQ JMS resources for [$queueName]", e)
            }
        }
    }
}
