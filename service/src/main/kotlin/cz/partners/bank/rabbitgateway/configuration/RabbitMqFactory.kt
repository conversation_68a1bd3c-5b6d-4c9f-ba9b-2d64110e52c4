package cz.partners.bank.rabbitgateway.configuration

import cz.partners.bank.rabbitgateway.rabbitapi.RabbitGatewayConstants.RECORD_VERSION_CHANGE_ACCOUNT_CONSUMER
import cz.partners.bank.rabbitgateway.rabbitapi.RabbitGatewayConstants.RECORD_VERSION_CHANGE_DIRECT_DEBIT_CONSUMER
import cz.partners.bank.rabbitgateway.rabbitapi.RabbitGatewayConstants.RECORD_VERSION_CHANGE_LOAN_CONSUMER
import cz.partners.bank.rabbitgateway.rabbitapi.RabbitGatewayConstants.RECORD_VERSION_CHANGE_SIPO_CONSUMER
import cz.partners.bank.rabbitgateway.rabbitapi.RabbitGatewayConstants.RECORD_VERSION_CHANGE_STANDING_ORDER_CONSUMER
import cz.partners.bank.rabbitgateway.rabbitapi.RabbitGatewayConstants.TERMINATION_RECORD_VERSION_CHANGE_ACCOUNT_CONSUMER
import io.micronaut.context.annotation.Factory

@Factory
class RabbitMqFactory {

    companion object {

        // producers for a new transaction service
        const val REQUIRED_TRANSACTION_PRODUCER = "bank-rabbit-gateway-bank-transaction-required-transaction"
        const val REALIZED_TRANSACTION_PRODUCER = "bank-rabbit-gateway-bank-transaction-realized-transaction"
        const val REALIZED_HOLD_PRODUCER = "bank-rabbit-gateway-bank-transaction-realized-hold"

        const val TREASURY_REQUIRED_TRANSACTION_PRODUCER = "bank-rabbit-gateway-bank-treasury-service-required-transaction"
        const val TREASURY_REALIZED_TRANSACTION_PRODUCER = "bank-rabbit-gateway-bank-treasury-service-realized-transaction"

        const val LOAN_REALIZED_TRANSACTION_PRODUCER = "bank-rabbit-gateway-bank-loan-realized-transaction"

        // bank-rabbit-gateway-incoming-... queues/exchanges should be named after AQ queue name due to bank-local-collector
        // e.g. for REQUIRED_TRANSACTION AQ the name should be bank-rabbit-gateway-incoming-required-transaction
        const val REQUIRED_INCOMING_PRODUCER = "bank-rabbit-gateway-incoming-required-transaction"
        const val REALIZED_INCOMING_PRODUCER = "bank-rabbit-gateway-incoming-realized-transaction"
        const val REALIZED_HOLD_INCOMING_PRODUCER = "bank-rabbit-gateway-incoming-realized-hold"
        const val REQUIRED_INCOMING_CONSUMER = REQUIRED_INCOMING_PRODUCER
        const val REALIZED_INCOMING_CONSUMER = REALIZED_INCOMING_PRODUCER
        const val REALIZED_HOLD_INCOMING_CONSUMER = REALIZED_HOLD_INCOMING_PRODUCER
        const val CHALLENGE_PAYMENT_REALIZED_PRODUCER = "bank-rabbit-gateway-bank-challenges-challenge-payment-realized"
        const val CHALLENGE_PAYMENT_CANCELLED_PRODUCER = "bank-rabbit-gateway-bank-challenges-challenge-payment-cancelled"

        /**
         * Record version change dequeue and queue for transactions
         */
        const val RECORD_VERSION_CHANGE_INCOMING_PRODUCER = "bank-rabbit-gateway-incoming-record-version-change"
        const val RECORD_VERSION_CHANGE_INCOMING_CONSUMER = RECORD_VERSION_CHANGE_INCOMING_PRODUCER
        const val RECORD_VERSION_CHANGE_STANDING_ORDER_PRODUCER = RECORD_VERSION_CHANGE_STANDING_ORDER_CONSUMER
        const val RECORD_VERSION_CHANGE_DIRECT_DEBIT_PRODUCER = RECORD_VERSION_CHANGE_DIRECT_DEBIT_CONSUMER
        const val RECORD_VERSION_CHANGE_SIPO_PRODUCER = RECORD_VERSION_CHANGE_SIPO_CONSUMER
        const val RECORD_VERSION_CHANGE_ACCOUNT_PRODUCER = RECORD_VERSION_CHANGE_ACCOUNT_CONSUMER
        const val TERMINATION_RECORD_VERSION_CHANGE_ACCOUNT_PRODUCER = TERMINATION_RECORD_VERSION_CHANGE_ACCOUNT_CONSUMER
        const val RECORD_VERSION_CHANGE_LOAN_PRODUCER = RECORD_VERSION_CHANGE_LOAN_CONSUMER
        const val LOANS_PAID_VALUES_CHANGE_PRODUCER = "bank-rabbit-gateway-loans-paid-values-change"

        const val DAILY_CLOSING_INCOMING_PRODUCER = "bank-rabbit-gateway-incoming-daily-closing"
        const val DAILY_CLOSING_INCOMING_CONSUMER = DAILY_CLOSING_INCOMING_PRODUCER

        //standing order
        const val DAILY_CLOSING_STANDING_ORDER_PRODUCER = "bank-rabbit-gateway-bank-standing-order-service-eod"

        // suspected fraud
        const val SUSPECTED_FRAUD_INCOMING_PRODUCER = "bank-rabbit-gateway-incoming-suspected-fraud"
        const val SUSPECTED_FRAUD_INCOMING_CONSUMER = "bank-rabbit-gateway-incoming-suspected-fraud"
        const val SUSPECTED_FRAUD_PRODUCER = "bank-rabbit-gateway-bank-fraud-detection-suspected-fraud"

        // planned transactions
        const val PLANNED_TRANSACTIONS_INCOMING_PRODUCER  = "bank-rabbit-gateway-incoming-planned-transactions"
        const val PLANNED_TRANSACTIONS_INCOMING_CONSUMER = "bank-rabbit-gateway-incoming-planned-transactions"
        const val PLANNED_TRANSACTIONS_PRODUCER = "bank-rabbit-gateway-bank-standing-order-planned-transactions"

        // reduced interest rate
        const val OUTGOING_MESSAGE_INCOMING_PRODUCER  = "bank-rabbit-gateway-incoming-outgoing-message"
        const val OUTGOING_MESSAGE_INCOMING_CONSUMER = "bank-rabbit-gateway-incoming-outgoing-message"
        const val REDUCED_INTEREST_RATE_PRODUCER = "bank-rabbit-gateway-bank-campaign-service-reduced-interest-rate"

        // account change
        const val ACCOUNT_CHANGES_INCOMING_PRODUCER = "bank-rabbit-gateway-incoming-account-changes"
        const val ACCOUNT_CHANGES_INCOMING_CONSUMER = ACCOUNT_CHANGES_INCOMING_PRODUCER
    }

}
