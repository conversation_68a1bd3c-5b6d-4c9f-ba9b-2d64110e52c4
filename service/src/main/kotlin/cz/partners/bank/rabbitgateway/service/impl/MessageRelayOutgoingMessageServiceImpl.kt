package cz.partners.bank.rabbitgateway.service.impl

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import cz.partners.bank.rabbitgateway.configuration.OracleAqConfiguration
import cz.partners.bank.rabbitgateway.exception.QueueParseException
import cz.partners.bank.rabbitgateway.model.core.GenericOutgoingMessageCore
import cz.partners.bank.rabbitgateway.model.core.LoanPaidValuesChangeCoreMessage
import cz.partners.bank.rabbitgateway.model.core.LoanPastDueChangeCoreMessage
import cz.partners.bank.rabbitgateway.model.core.OutgoingMessageCore
import cz.partners.bank.rabbitgateway.model.core.ReducedInterestRateCoreMessage
import cz.partners.bank.rabbitgateway.producer.LoanProducer
import cz.partners.bank.rabbitgateway.producer.OutgoingMessageIncomingProducer
import cz.partners.bank.rabbitgateway.producer.ReducedInterestRateProducer
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.ReducedInterestRateMessage
import cz.partners.bank.rabbitgateway.rabbitapi.LoanVersionChangeMessage
import cz.partners.bank.rabbitgateway.service.OracleAqJmsService
import cz.partners.bank.rabbitgateway.service.OracleDataSource
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bindSync
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
open class MessageRelayOutgoingMessageServiceImpl(
    private val reducedInterestRateProducer: ReducedInterestRateProducer,
    private val loanProducer: LoanProducer,
    oracleDataSource: OracleDataSource,
    incomingProducer: OutgoingMessageIncomingProducer,
    objectMapper: ObjectMapper,
    oracleAqJmsService: OracleAqJmsService,
    oracleAqConfig: OracleAqConfiguration
) : MessageRelayCommonServiceImpl<OutgoingMessageCore>(
    incomingProducer,
    objectMapper,
    oracleDataSource,
    oracleAqJmsService,
    oracleAqConfig
) {
    companion object {
        const val OUTGOING_MESSAGE_DCS_QUEUE = "OUTGOING_MESSAGE"
    }

    override val logger = LoggerFactory.getLogger(this::class.java)
    
    override val queueName: String = OUTGOING_MESSAGE_DCS_QUEUE

    override fun dequeueMessage(
        instance: String,
        timeoutSeconds: Int,
    ): Either<PbkException, String> = oracleDataSource.dequeueMessage(
        queueName = OUTGOING_MESSAGE_DCS_QUEUE,
        instance = instance,
        timeoutSeconds = timeoutSeconds
    )

    override fun unmarshalResult(
        rawResult: String,
    ): Either<QueueParseException, OutgoingMessageCore> = bindSync {
        !unmarshalResultHelper<OutgoingMessageCore>(rawResult)
    }

    override fun produceResult(message: OutgoingMessageCore): Either<PbkException, Unit> = bindSync {
        logger.debug("Received outgoing message from CORE. Message: [{}].", message)
        when (message) {
            is ReducedInterestRateCoreMessage -> message.publishAsReducedInterestRate()
            is LoanPaidValuesChangeCoreMessage -> message.publishAsLoansPaidValuesChange()
            is LoanPastDueChangeCoreMessage -> message.publishAsLoanPastDueChange()
            is GenericOutgoingMessageCore -> message.logAsUnsupported()
        }
    }

    private fun GenericOutgoingMessageCore.logAsUnsupported() {
        logger.warn("Unsupported message type [{}]. Message: [{}].", messageType, this)
    }

    private fun ReducedInterestRateCoreMessage.publishAsReducedInterestRate() {
        val message = toReducedInterestRateMessage()
        reducedInterestRateProducer.publish(message)
        logger.info("Interest rate reducing message has been sent. Message: [{}].", message)
    }

    private fun LoanPaidValuesChangeCoreMessage.publishAsLoansPaidValuesChange() {
        if (loanIds.isNotEmpty()) {
            loanIds.forEach {
                loanProducer.recordVersionChangeLoan(
                    LoanVersionChangeMessage(
                        coreId = it,
                        eventTimestamp = eventTimestamp
                    )
                )
            }
            logger.info("Loan Paid Values Change message has been processed. Message: [{}].", this)
        }
    }

    private fun LoanPastDueChangeCoreMessage.publishAsLoanPastDueChange() {
        if (loans.isNotEmpty()) {
            loans.forEach {
                loanProducer.recordVersionChangeLoan(
                    LoanVersionChangeMessage(
                        coreId = it.idLoan,
                        eventTimestamp = eventTimestamp
                    )
                )
            }
            logger.info("Loan Past Due Change message has been processed. Message: [{}].", this)
        }
    }

    private fun ReducedInterestRateCoreMessage.toReducedInterestRateMessage() =
        ReducedInterestRateMessage(
            subjectExternalIds = subjectExternalIds,
            validFrom = validFrom,
            messageOrderNo = messageOrderNo,
            eventTimestamp = eventTimestamp,
            lastMessage = lastMessage
        )
}
