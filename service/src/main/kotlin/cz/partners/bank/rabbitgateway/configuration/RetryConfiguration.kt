package cz.partners.bank.rabbitgateway.configuration

import cz.partners.bank.rabbitgateway.service.ExponentialBackoffRetryPolicy
import cz.partners.bank.rabbitgateway.service.FixedDelayRetryPolicy
import cz.partners.bank.rabbitgateway.service.NoRetryPolicy
import cz.partners.bank.rabbitgateway.service.RetryPolicy
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Factory
import jakarta.inject.Singleton

/**
 * Configuration properties for retry behavior
 */
@ConfigurationProperties("oracle.aq.retry")
data class RetryConfiguration(
    /**
     * Maximum number of retry attempts
     */
    val maxAttempts: Int = 3,
    
    /**
     * Initial delay in milliseconds for exponential backoff
     */
    val initialDelayMs: Long = 1000,
    
    /**
     * Maximum delay in milliseconds for exponential backoff
     */
    val maxDelayMs: Long = 60000,
    
    /**
     * Multiplier for exponential backoff
     */
    val multiplier: Double = 2.0,
    
    /**
     * Retry strategy: "exponential", "fixed", or "none"
     */
    val strategy: String = "exponential",
    
    /**
     * Fixed delay in milliseconds (used when strategy is "fixed")
     */
    val fixedDelayMs: Long = 5000
)

/**
 * Configuration properties for poison message handling
 */
@ConfigurationProperties("oracle.aq.poison-message")
data class PoisonMessageConfiguration(
    /**
     * Maximum number of delivery attempts before considering a message as poison
     */
    val maxDeliveryAttempts: Int = 3,
    
    /**
     * Whether to enable poison message detection
     */
    val enabled: Boolean = true,
    
    /**
     * Property name to check for delivery count (Oracle AQ specific)
     */
    val deliveryCountProperty: String = "JMS_OracleDeliveryCount"
)

/**
 * Factory for creating retry policy beans
 */
@Factory
class RetryPolicyFactory {
    
    @Singleton
    fun retryPolicy(config: RetryConfiguration): RetryPolicy {
        return when (config.strategy.lowercase()) {
            "exponential" -> ExponentialBackoffRetryPolicy(
                maxAttempts = config.maxAttempts,
                initialDelayMs = config.initialDelayMs,
                maxDelayMs = config.maxDelayMs,
                multiplier = config.multiplier
            )
            "fixed" -> FixedDelayRetryPolicy(
                maxAttempts = config.maxAttempts,
                delayMs = config.fixedDelayMs
            )
            "none" -> NoRetryPolicy()
            else -> {
                // Default to exponential backoff for unknown strategies
                ExponentialBackoffRetryPolicy(
                    maxAttempts = config.maxAttempts,
                    initialDelayMs = config.initialDelayMs,
                    maxDelayMs = config.maxDelayMs,
                    multiplier = config.multiplier
                )
            }
        }
    }
}
