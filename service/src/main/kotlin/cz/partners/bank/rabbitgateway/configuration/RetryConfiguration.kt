package cz.partners.bank.rabbitgateway.configuration

import cz.partners.bank.rabbitgateway.service.ExponentialBackoffRetryPolicy
import cz.partners.bank.rabbitgateway.service.RetryPolicy
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Factory
import jakarta.inject.Singleton

/**
 * Simple configuration for Oracle AQ JMS behavior
 */
@ConfigurationProperties("oracle.aq")
data class OracleAqConfiguration(
    /**
     * Maximum number of retry attempts for connection issues (default: 3)
     */
    val maxRetryAttempts: Int = 3
)

/**
 * Factory for creating retry policy with simple defaults
 */
@Factory
class RetryPolicyFactory {

    @Singleton
    fun retryPolicy(config: OracleAqConfiguration): RetryPolicy {
        // Simple exponential backoff: 1s, 2s, 4s, max 30s
        return ExponentialBackoffRetryPolicy(
            maxAttempts = config.maxRetryAttempts,
            initialDelayMs = 1000,
            maxDelayMs = 30000,
            multiplier = 2.0
        )
    }
}
