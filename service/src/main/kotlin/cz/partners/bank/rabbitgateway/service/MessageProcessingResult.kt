package cz.partners.bank.rabbitgateway.service

/**
 * Simple result of message processing
 */
sealed class MessageProcessingResult {
    /**
     * Message was processed successfully and should be committed
     */
    object Success : MessageProcessingResult()

    /**
     * Message processing failed and should be rolled back for retry
     */
    data class Failure(val error: Exception) : MessageProcessingResult()
}