package cz.partners.bank.rabbitgateway.service

/**
 * Result of message processing that determines transaction outcome
 */
sealed class MessageProcessingResult {
    /**
     * Message was processed successfully and should be committed
     */
    object Success : MessageProcessingResult()

    /**
     * Message processing failed and should be rolled back for retry
     */
    data class Failure(val error: Exception) : MessageProcessingResult()

    /**
     * Message is a poison message and should be discarded (committed)
     */
    data class PoisonMessage(val reason: String) : MessageProcessingResult()
}