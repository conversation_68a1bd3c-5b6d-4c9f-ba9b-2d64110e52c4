package cz.partners.bank.rabbitgateway.service

import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import org.slf4j.LoggerFactory
import kotlin.math.min
import kotlin.math.pow

/**
 * Interface for retry policies used in message processing
 */
interface RetryPolicy {
    /**
     * Executes a block with retry logic
     * 
     * @param instance Instance identifier for logging
     * @param block The code to execute with retry
     * @return The result of successful execution
     * @throws Exception if all retry attempts are exhausted
     */
    suspend fun <T> executeWithRetry(instance: String, block: suspend () -> T): T
}

/**
 * Exponential backoff retry policy implementation
 */
class ExponentialBackoffRetryPolicy(
    private val maxAttempts: Int = 3,
    private val initialDelayMs: Long = 1000,
    private val maxDelayMs: Long = 60000,
    private val multiplier: Double = 2.0
) : RetryPolicy {
    
    private val logger = LoggerFactory.getLogger(ExponentialBackoffRetryPolicy::class.java)
    
    override suspend fun <T> executeWithRetry(instance: String, block: suspend () -> T): T {
        var attempt = 0
        var lastException: Exception? = null
        
        while (attempt < maxAttempts) {
            try {
                return block()
            } catch (e: CancellationException) {
                // Don't retry cancellation - propagate immediately
                logger.debug("[$instance] Operation cancelled, not retrying")
                throw e
            } catch (e: Exception) {
                lastException = e
                attempt++
                
                if (attempt >= maxAttempts) {
                    logger.error("[$instance] All retry attempts exhausted (${maxAttempts}), giving up", e)
                    break
                }
                
                val delayMs = calculateDelay(attempt)
                logger.warn("[$instance] Attempt $attempt failed, retrying in ${delayMs}ms", e)
                delay(delayMs)
            }
        }
        
        // If we get here, all attempts failed
        throw lastException ?: RuntimeException("All retry attempts failed")
    }
    
    /**
     * Calculates the delay for the given attempt using exponential backoff
     */
    private fun calculateDelay(attempt: Int): Long {
        return min(
            (initialDelayMs * multiplier.pow(attempt - 1)).toLong(),
            maxDelayMs
        )
    }
}

/**
 * No-retry policy that executes the block once without any retry logic
 */
class NoRetryPolicy : RetryPolicy {
    override suspend fun <T> executeWithRetry(instance: String, block: suspend () -> T): T {
        return block()
    }
}

/**
 * Fixed delay retry policy that waits a constant amount of time between retries
 */
class FixedDelayRetryPolicy(
    private val maxAttempts: Int = 3,
    private val delayMs: Long = 1000
) : RetryPolicy {
    
    private val logger = LoggerFactory.getLogger(FixedDelayRetryPolicy::class.java)
    
    override suspend fun <T> executeWithRetry(instance: String, block: suspend () -> T): T {
        var attempt = 0
        var lastException: Exception? = null
        
        while (attempt < maxAttempts) {
            try {
                return block()
            } catch (e: CancellationException) {
                logger.debug("[$instance] Operation cancelled, not retrying")
                throw e
            } catch (e: Exception) {
                lastException = e
                attempt++
                
                if (attempt >= maxAttempts) {
                    logger.error("[$instance] All retry attempts exhausted (${maxAttempts}), giving up", e)
                    break
                }
                
                logger.warn("[$instance] Attempt $attempt failed, retrying in ${delayMs}ms", e)
                delay(delayMs)
            }
        }
        
        throw lastException ?: RuntimeException("All retry attempts failed")
    }
}
