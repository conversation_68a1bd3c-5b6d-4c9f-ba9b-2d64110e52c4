package cz.partners.bank.rabbitgateway.service

import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import org.slf4j.LoggerFactory
import kotlin.math.min
import kotlin.math.pow

/**
 * Simple retry policy for Oracle AQ JMS operations
 */
interface RetryPolicy {
    suspend fun <T> executeWithRetry(instance: String, block: suspend () -> T): T
}

/**
 * Simple exponential backoff retry policy
 * Retries with delays: 1s, 2s, 4s, 8s, etc. (capped at maxDelayMs)
 */
class ExponentialBackoffRetryPolicy(
    private val maxAttempts: Int = 3,
    private val initialDelayMs: Long = 1000,
    private val maxDelayMs: Long = 30000,
    private val multiplier: Double = 2.0
) : RetryPolicy {

    private val logger = LoggerFactory.getLogger(ExponentialBackoffRetryPolicy::class.java)

    override suspend fun <T> executeWithRetry(instance: String, block: suspend () -> T): T {
        var attempt = 0

        while (attempt < maxAttempts) {
            try {
                return block()
            } catch (e: CancellationException) {
                logger.debug("[$instance] Operation cancelled")
                throw e
            } catch (e: Exception) {
                attempt++

                if (attempt >= maxAttempts) {
                    logger.error("[$instance] All $maxAttempts retry attempts failed", e)
                    throw e
                }

                val delayMs = min(
                    (initialDelayMs * multiplier.pow(attempt - 1)).toLong(),
                    maxDelayMs
                )
                logger.warn("[$instance] Attempt $attempt failed, retrying in ${delayMs}ms", e)
                delay(delayMs)
            }
        }

        throw RuntimeException("Retry logic error") // Should never reach here
    }
}
