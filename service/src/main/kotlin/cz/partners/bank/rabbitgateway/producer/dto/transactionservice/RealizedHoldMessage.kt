package cz.partners.bank.rabbitgateway.producer.dto.transactionservice

import cz.partners.bank.common.api.contract.AmountIsoCurrency
import cz.partners.bank.core.api.BankAccountIdentification
import cz.partners.bank.core.api.ChannelCode
import cz.partners.bank.core.api.OriginalAmount
import cz.partners.bank.core.api.TransactionTypeCode
import cz.partners.bank.rabbitgateway.model.core.Balances
import cz.partners.bank.rabbitgateway.model.core.CancelHoldReason
import cz.partners.bank.rabbitgateway.util.maskAccountNumber
import cz.partners.bank.rabbitgateway.util.maskMessage
import cz.partners.bank.rabbitgateway.util.maskName
import cz.partners.bank.rabbitgateway.util.maskSensitive
import io.micronaut.core.annotation.Introspected
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime


@Introspected
data class RealizedHoldMessage(
    val requiredTransactionId: Long,
    val realizedHoldId: Long,
    val transactionChainId: Long,
    val externalId: String?,
    val paymentCardId: Long,
    val accountIdentification: BankAccountIdentification,
    val balances: Balances,
    val amount: AmountIsoCurrency,
    val originalAmount: OriginalAmount?,
    val createdAt: OffsetDateTime,
    val eventTimestamp: OffsetDateTime,
    val channelCode: ChannelCode,
    val merchantId: String?,
    val realizationDate: LocalDate,
    val textMessage: String?,
    val transactionTypeCode: TransactionTypeCode,
    val valutaDate: LocalDate,
    val actionTypeCode: String,
    val ecbDeviation: BigDecimal?,
    val cancellationReason: CancelHoldReason?,
) {
    override fun toString(): String {
        return "RealizedHoldMessage(" +
            "requiredTransactionId=$requiredTransactionId, " +
            "realizedHoldId=$realizedHoldId, " +
            "transactionChainId=$transactionChainId, " +
            "externalId=$externalId, " +
            "paymentCardId=$paymentCardId, " +
            "accountIdentification=BankAccountIdentification(" +
                "accountId=${accountIdentification.accountId}, " +
                "accountNumber=${accountIdentification.accountNumber.maskAccountNumber()}, " +
                "accountName=${accountIdentification.accountName.maskName()}, " +
                "bankCode=${accountIdentification.bankCode}, " +
                "isTechnical=${accountIdentification.isTechnical}, " +
                "isInternal=${accountIdentification.isInternal}" +
            "), " +
            "balances=Balances(" +
                "date=${balances.date}, " +
                "amountAvailableBalance=${balances.amountAvailableBalance.maskSensitive()}, " +
                "amountAvailableBalanceWithoutCardHolds=${balances.amountAvailableBalanceWithoutCardHolds.maskSensitive()}, " +
                "accountBalance=${balances.amountAccountBalance.maskSensitive()}, " +
                "currencyCode=${balances.currencyCode}" +
            "), " +
            "amount=AmountIsoCurrency(" +
                "value=${amount.value.maskSensitive()}, " +
                "currencyCode=${amount.currencyCode}" +
            "originalAmount=OriginalAmount(" +
                "value=${originalAmount?.value?.maskSensitive()}, " +
                "currencyCode=${originalAmount?.currencyCode}, " +
                "rate=${originalAmount?.rate}" +
            "), " +
            "createdAt=$createdAt, " +
            "eventTimestamp=$eventTimestamp, " +
            "channelCode=$channelCode, " +
            "merchantId=$merchantId, " +
            "realizationDate=$realizationDate, " +
            "textMessage=${textMessage.maskMessage()}, " +
            "transactionTypeCode=$transactionTypeCode, " +
            "valutaDate=$valutaDate, " +
            "actionTypeCode=$actionTypeCode, " +
            "ecbDeviation=$ecbDeviation, " +
            "cancellationReason=$cancellationReason" +
            ")"
    }
}
