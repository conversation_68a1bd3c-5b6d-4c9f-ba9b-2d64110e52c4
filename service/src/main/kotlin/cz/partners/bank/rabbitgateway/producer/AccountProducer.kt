package cz.partners.bank.rabbitgateway.producer

import arrow.core.Either
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.RECORD_VERSION_CHANGE_ACCOUNT_PRODUCER
import cz.partners.bank.rabbitgateway.rabbitapi.AccountVersionChangeMessage
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bindSync
import cz.pbktechnology.platform.common.rabbitmq.Producer
import cz.pbktechnology.platform.common.rabbitmq.RabbitConfiguration
import jakarta.inject.Singleton
import javax.annotation.PostConstruct
import org.slf4j.LoggerFactory

@Singleton
class AccountProducer(
    private val rabbitConfiguration: RabbitConfiguration,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private lateinit var recordVersionChangeAccountProducer: Producer<AccountVersionChangeMessage>

    @PostConstruct
    fun init() {
        recordVersionChangeAccountProducer = rabbitConfiguration.initProducer(RECORD_VERSION_CHANGE_ACCOUNT_PRODUCER)
    }

    fun recordVersionChangeAccount(message: AccountVersionChangeMessage): Either<PbkException, Unit> = bindSync {
        recordVersionChangeAccountProducer.publish(message)
        logger.info("Sent [$RECORD_VERSION_CHANGE_ACCOUNT_PRODUCER] message: [$message]")
    }
}