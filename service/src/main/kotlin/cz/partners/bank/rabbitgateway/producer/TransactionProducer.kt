package cz.partners.bank.rabbitgateway.producer

import arrow.core.Either
import cz.partners.bank.core.api.BankAccountIdentification
import cz.partners.bank.core.api.RequiredTransactionStatus
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.CHALLENGE_PAYMENT_CANCELLED_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.CHALLENGE_PAYMENT_REALIZED_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.LOAN_REALIZED_TRANSACTION_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.REALIZED_HOLD_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.REALIZED_TRANSACTION_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.RECORD_VERSION_CHANGE_DIRECT_DEBIT_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.RECORD_VERSION_CHANGE_SIPO_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.RECORD_VERSION_CHANGE_STANDING_ORDER_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.REQUIRED_TRANSACTION_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.TREASURY_REALIZED_TRANSACTION_PRODUCER
import cz.partners.bank.rabbitgateway.configuration.RabbitMqFactory.Companion.TREASURY_REQUIRED_TRANSACTION_PRODUCER
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.ChallengePaymentCancelledMessage
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.ChallengePaymentRealizedMessage
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.LoanRealizedTransactionMessage
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.RealizedHoldMessage
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.RealizedTransactionMessage
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.RequiredTransactionMessage
import cz.partners.bank.rabbitgateway.producer.dto.transactionservice.TransactionEntityType
import cz.partners.bank.rabbitgateway.rabbitapi.TransactionVersionChangeMessage
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bindSync
import cz.pbktechnology.platform.common.rabbitmq.Producer
import cz.pbktechnology.platform.common.rabbitmq.RabbitConfiguration
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import java.util.UUID
import javax.annotation.PostConstruct

@Singleton
class TransactionProducer(
    private val rabbitConfiguration: RabbitConfiguration,
    @Value("\${loan.realized-transactions.publish}")
    val loanRealizedTransactionsPublish: Boolean
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private lateinit var recordVersionChangeDirectDebitProducer: Producer<TransactionVersionChangeMessage>
    private lateinit var recordVersionChangeStandingOrderProducer: Producer<TransactionVersionChangeMessage>
    private lateinit var recordVersionChangeDirectSipoProducer: Producer<TransactionVersionChangeMessage>
    private lateinit var treasuryRequiredTransactionProducer: Producer<RequiredTransactionMessage>
    private lateinit var treasuryRealizedTransactionProducer: Producer<RealizedTransactionMessage>

    private lateinit var loanRealizedTransactionProducer: Producer<LoanRealizedTransactionMessage>

    //  Producers for a new transaction service
    private lateinit var requiredTransactionProducer: Producer<RequiredTransactionMessage>
    private lateinit var realizedTransactionProducer: Producer<RealizedTransactionMessage>
    private lateinit var cardHoldProducerProducer: Producer<RealizedHoldMessage>
    private lateinit var challengePaymentRealizedProducer: Producer<ChallengePaymentRealizedMessage>
    private lateinit var challengePaymentCancelledProducer: Producer<ChallengePaymentCancelledMessage>

    @Value("\${treasury.account.account-number}")
    private lateinit var treasuryAccountNumber: String

    @Value("\${treasury.account.bank-code}")
    private lateinit var treasuryAccountBankCode: String

    @PostConstruct
    fun init() {
        treasuryRequiredTransactionProducer = rabbitConfiguration.initProducer(TREASURY_REQUIRED_TRANSACTION_PRODUCER)
        treasuryRealizedTransactionProducer = rabbitConfiguration.initProducer(TREASURY_REALIZED_TRANSACTION_PRODUCER)
        recordVersionChangeDirectDebitProducer = rabbitConfiguration.initProducer(RECORD_VERSION_CHANGE_DIRECT_DEBIT_PRODUCER)
        recordVersionChangeStandingOrderProducer = rabbitConfiguration.initProducer(RECORD_VERSION_CHANGE_STANDING_ORDER_PRODUCER)
        recordVersionChangeDirectSipoProducer = rabbitConfiguration.initProducer(RECORD_VERSION_CHANGE_SIPO_PRODUCER)
        requiredTransactionProducer = rabbitConfiguration.initProducer(REQUIRED_TRANSACTION_PRODUCER)
        realizedTransactionProducer = rabbitConfiguration.initProducer(REALIZED_TRANSACTION_PRODUCER)
        loanRealizedTransactionProducer = rabbitConfiguration.initProducer(LOAN_REALIZED_TRANSACTION_PRODUCER)
        cardHoldProducerProducer = rabbitConfiguration.initProducer(REALIZED_HOLD_PRODUCER)
        challengePaymentRealizedProducer = rabbitConfiguration.initProducer(CHALLENGE_PAYMENT_REALIZED_PRODUCER)
        challengePaymentCancelledProducer = rabbitConfiguration.initProducer(CHALLENGE_PAYMENT_CANCELLED_PRODUCER)
    }

    fun realizedTransaction(
        message: RealizedTransactionMessage,
    ): Either<PbkException, Unit> = bindSync {

        if (message.creditAccountIdentification.isTreasury || message.debitAccountIdentification.isTreasury) {
            treasuryRealizedTransactionProducer.publish(message)
            logger.info("Sent [$TREASURY_REALIZED_TRANSACTION_PRODUCER] message: [$message]")
        } else {
            realizedTransactionProducer.publish(message)
            logger.info("Sent [$REALIZED_TRANSACTION_PRODUCER] message: [$message]")

            if (message.coreEntityType == TransactionEntityType.DCS_LOAN && message.coreEntityId != null && loanRealizedTransactionsPublish) {
                loanRealizedTransactionProducer.publish(
                    LoanRealizedTransactionMessage(
                        loanCoreId = message.coreEntityId
                    )
                )
                logger.info("Sent [$LOAN_REALIZED_TRANSACTION_PRODUCER] message: [$message]")
            }

            if (message.externalId != null && message.isChallengePayment) {
                challengePaymentRealizedProducer.publish(
                    ChallengePaymentRealizedMessage(
                        UUID.fromString(message.externalId.substringAfterLast(":"))
                    )
                )
            }
        }
    }

    fun requiredTransaction(
        message: RequiredTransactionMessage,
    ): Either<PbkException, Unit> = bindSync {
        if (message.creditAccountIdentification.isTreasury || message.debitAccountIdentification.isTreasury) {
            treasuryRequiredTransactionProducer.publish(message)
            logger.info("Sent [$TREASURY_REQUIRED_TRANSACTION_PRODUCER] message: [$message]")
        } else {
            requiredTransactionProducer.publish(message)
            logger.info("Sent [$REQUIRED_TRANSACTION_PRODUCER] message: [$message]")

            if (message.externalId != null && message.isChallengePayment && message.status in transactionCancelledStates) {
                challengePaymentCancelledProducer.publish(
                    ChallengePaymentCancelledMessage(
                        UUID.fromString(message.externalId.substringAfterLast(":"))
                    )
                )
            }
        }
    }

    fun cardHold(
        message: RealizedHoldMessage,
    ): Either<PbkException, Unit> = bindSync {
        cardHoldProducerProducer.publish(message)
        logger.info("Sent [$REALIZED_HOLD_PRODUCER] message: [$message]")
    }

    fun recordVersionChangeDirectDebit(message: TransactionVersionChangeMessage): Either<PbkException, Unit> = bindSync {
        recordVersionChangeDirectDebitProducer.publish(message)
        logger.info("Sent [$RECORD_VERSION_CHANGE_DIRECT_DEBIT_PRODUCER] message: [$message]")
    }

    fun recordVersionChangeStandingOrder(message: TransactionVersionChangeMessage): Either<PbkException, Unit> = bindSync {
        recordVersionChangeStandingOrderProducer.publish(message)
        logger.info("Sent [$RECORD_VERSION_CHANGE_STANDING_ORDER_PRODUCER] message: [$message]")
    }

    fun recordVersionChangeSipo(message: TransactionVersionChangeMessage): Either<PbkException, Unit> = bindSync {
        recordVersionChangeDirectSipoProducer.publish(message)
        logger.info("Sent [$RECORD_VERSION_CHANGE_SIPO_PRODUCER] message: [$message]")
    }

    private val BankAccountIdentification.isTreasury
        get() = accountNumber == treasuryAccountNumber && bankCode == treasuryAccountBankCode

    private val RealizedTransactionMessage.isChallengePayment
        get() = externalId?.substringBefore(":") == "CHALLENGES"

    private val RequiredTransactionMessage.isChallengePayment
        get() = externalId?.substringBefore(":") == "CHALLENGES"

    private val transactionCancelledStates = setOf(
        RequiredTransactionStatus.CANCELLED,
        RequiredTransactionStatus.UNHOLDED,
        RequiredTransactionStatus.SUSPENDED,
        RequiredTransactionStatus.REVERTED,
        RequiredTransactionStatus.EXPIRED,
        RequiredTransactionStatus.REJECTED,
    )
}
