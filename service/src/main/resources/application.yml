global:
  usePolling: ${GL<PERSON><PERSON>L_USE_POLLING:true}
looper:
  required:
    enabled: ${LOOPER_REQUIRED_ENABLED:false}
  realized:
    enabled: ${LOOPER_REALIZED_ENABLED:false}
  card-hold:
    enabled: ${LOOPER_REALIZED_CARD_HOLD_ENABLED:false}
  record-version-change:
    enabled: ${LOOPER_REALIZED_RECORD_VERSION_CHANGE_ENABLED:false}
  daily-closing:
    enabled: ${LOOPER_DAILY_CLOSING_ENABLED:false}
  suspected-fraud:
    enabled: ${LOOPER_SUSPECTED_FRAUD_ENABLED:false}
  planned-transactions:
    enabled: ${LOOPER_PLANNED_TRANSACTIONS_ENABLED:false}
  outgoing-message:
    enabled: ${LOOPER_OUTGOING_MESSAGE_ENABLED:false}
  account-changes:
    enabled: ${LOOPER_ACCOUNT_CHANGES_ENABLED:false}

micronaut:
  application:
    name: bank-rabbit-gateway

  server:
    port: ${SERVER_PORT:9088}
  router:
    static-resources:
      swagger:
        paths: classpath:META-INF/swagger
        mapping: /swagger/**
      redoc:
        paths: classpath:META-INF/swagger/views/redoc
        mapping: /redoc/**
      rapidoc:
        paths: classpath:META-INF/swagger/views/rapidoc
        mapping: /rapidoc/**
      swagger-ui:
        paths: classpath:META-INF/swagger/views/swagger-ui
        mapping: /swagger-ui/**

  metrics:
    enabled: true
    export:
      prometheus:
        descriptions: true
        enabled: true
        step: PT1M


endpoints:
  health:
    details-visible: anonymous
    jdbc:
      enabled: false
  info:
    enabled: true
    sensitive: false
  prometheus:
    sensitive: false
  liquibase:
    enabled: true
    sensitive: false
  caches:
    enabled: true
    sensitive: false

#pro přístup k AQ na DCS 3 použij profil dle application-dcs3.yml.example
# Oracle AQ JMS uses the datasource settings by default
# The schema for queues is the same as the database username
datasources:
  default:
    url: ${JDBC_URL:`jdbc:h2:mem:devDb`}
    username: ${JDBC_USER:dcs2_RabbitMq}
    password: ${JDBC_PASSWORD:NADCS2SEUZZLOCALUNELEZE}
    driverClassName: ${JDBC_DRIVER:org.h2.Driver}
    maxLifetime: 600000
    maximumPoolSize: ${MAX_ORACLE_POOLSIZE:10}
    minimumIdle: ${MINIMUM_IDLE:1}
    leakDetectionThreshold: ${LEAK_DETECTION_THRESHOLD:30000}
    connection-timeout: 5000


jackson:
  deserialization:
    #    FAIL_ON_UNKNOWN_PROPERTIES: true # for testing a new dto
    ADJUST_DATES_TO_CONTEXT_TIME_ZONE: false
    READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE: true
  serialization:
    WRITE_DATES_AS_TIMESTAMPS: false


rabbitmq:
  host: ${RABBITMQ_HOST:localhost}
  port: ${RABBITMQ_PORT:5672}
  username: ${RABBITMQ_USER:guest}
  password: ${RABBITMQ_PASS:guest}
  consumer-tag-prefix: ${RABBITMQ_CONSUMER_TAG_PREFIX:localhost}
  max-dynamic-entries: 15

  producers:
    bank-rabbit-gateway-bank-treasury-service-required-transaction:
    bank-rabbit-gateway-bank-treasury-service-realized-transaction:
    bank-rabbit-gateway-incoming-required-transaction:
    bank-rabbit-gateway-incoming-realized-transaction:
    bank-rabbit-gateway-incoming-realized-hold:
    bank-rabbit-gateway-incoming-planned-transactions:
    bank-record-version-change-direct-debit:
    bank-record-version-change-standing-order:
    bank-record-version-change-sipo:
    bank-record-version-change-account:
    bank-rabbit-gateway-bank-accounts-termination-bank-record-version-change-account:
    bank-record-version-change-loan:
    bank-rabbit-gateway-incoming-record-version-change:
    bank-rabbit-gateway-incoming-daily-closing:
    bank-rabbit-gateway-incoming-suspected-fraud:
    bank-rabbit-gateway-bank-standing-order-service-eod:
    bank-rabbit-gateway-bank-transaction-required-transaction:
    bank-rabbit-gateway-bank-transaction-realized-transaction:
    bank-rabbit-gateway-bank-transaction-realized-hold:
    bank-rabbit-gateway-bank-loan-realized-transaction:
    bank-rabbit-gateway-bank-challenges-challenge-payment-realized:
    bank-rabbit-gateway-bank-challenges-challenge-payment-cancelled:
    bank-rabbit-gateway-bank-fraud-detection-suspected-fraud:
    bank-rabbit-gateway-bank-standing-order-planned-transactions:
    bank-rabbit-gateway-incoming-outgoing-message:
    bank-rabbit-gateway-bank-campaign-service-reduced-interest-rate:
    bank-rabbit-gateway-incoming-account-changes:
  consumers:
    bank-rabbit-gateway-incoming-required-transaction:
      max-retry: 1
    bank-rabbit-gateway-incoming-realized-transaction:
      max-retry: 1
    bank-rabbit-gateway-incoming-realized-hold:
      max-retry: 1
    bank-rabbit-gateway-incoming-record-version-change:
      max-retry: 1
    bank-rabbit-gateway-incoming-daily-closing:
      max-retry: 1
    bank-rabbit-gateway-incoming-suspected-fraud:
      max-retry: 1
    bank-rabbit-gateway-incoming-planned-transactions:
      max-retry: 1
    bank-rabbit-gateway-incoming-outgoing-message:
      max-retry: 1
    bank-rabbit-gateway-incoming-account-changes:
      max-retry: 1

treasury:
  account:
    account-number: ${TREASURY_ACCOUNT_ACCOUNTNUMBER:*********}
    bank-code: ${TREASURY_ACCOUNT_BANKCODE:6363}

logger:
  # (Optional) Enables Loki appender
  loki-enabled: ${LOKI_ENABLED:false}
  # Loki hostname
  loki-host: ${LOKI_HOST:`http://localhost:3100`}
  # Loki basic auth
  loki-user: ${LOKI_USER:user}
  # Loki basic auth
  loki-password: ${LOKI_PASSWORD:password}
  # Loki tennant
  loki-tenant: ${LOKI_TENANT:bank}
  # (Optional) set debug logger level for cz.partners package
  console-json:
    enabled: ${CONSOLE_JSON_ENABLED:false}

loan:
  realized-transactions:
    publish: ${LOAN_REALIZED_TRANSACTION_ENABLED:false}

bank:
  record-version-change:
    dcs-account:
      processing-enabled: ${BANK_RECORD_VERSION_CHANGE_DCS_ACCOUNT_PROCESSING_ENABLED:true}
      termination-processing-enabled: ${BANK_RECORD_VERSION_CHANGE_DCS_ACCOUNT_TERMINATION_PROCESSING_ENABLED:false}
