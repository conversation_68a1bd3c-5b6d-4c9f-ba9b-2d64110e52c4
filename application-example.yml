# Example configuration for Oracle AQ JMS
oracle:
  aq:
    # How many times to retry connection failures (default: 3)
    max-retry-attempts: 3
    
    # How many times a message can be delivered before being discarded as poison (default: 5)
    max-delivery-attempts: 5

# That's it! Simple configuration with sensible defaults.
# 
# Connection retry uses exponential backoff: 1s, 2s, 4s (max 30s)
# Poison messages are automatically detected using Oracle's JMS_OracleDeliveryCount property
