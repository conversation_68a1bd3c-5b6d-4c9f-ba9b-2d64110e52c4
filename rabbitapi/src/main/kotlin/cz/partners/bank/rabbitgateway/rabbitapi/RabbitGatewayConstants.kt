package cz.partners.bank.rabbitgateway.rabbitapi

object RabbitGatewayConstants {

    /**
     * this queue used RecordVersionChangeMessage
     */
    const val RECORD_VERSION_CHANGE_DIRECT_DEBIT_CONSUMER = "bank-record-version-change-direct-debit"
    const val RECORD_VERSION_CHANGE_STANDING_ORDER_CONSUMER = "bank-record-version-change-standing-order"
    const val RECORD_VERSION_CHANGE_SIPO_CONSUMER = "bank-record-version-change-sipo"
    const val RECORD_VERSION_CHANGE_ACCOUNT_CONSUMER = "bank-record-version-change-account"
    const val TERMINATION_RECORD_VERSION_CHANGE_ACCOUNT_CONSUMER = "bank-rabbit-gateway-bank-accounts-termination-bank-record-version-change-account"
    const val RECORD_VERSION_CHANGE_LOAN_CONSUMER = "bank-record-version-change-loan"

}
