package cz.partners.bank.rabbitgateway.rabbitapi

import io.micronaut.core.annotation.Introspected
import java.math.BigDecimal
import java.time.OffsetDateTime

@Introspected
data class AccountVersionChangeMessage(
    val coreId: Long,
    val recordVersion: Int,
    val eventTimestamp: OffsetDateTime,
    val status: String? = null,
    val totalSettlementAmount: BigDecimal? = BigDecimal.ZERO,
) {
    override fun toString(): String {
        return "AccountVersionChangeMessage(" +
                "coreId=$coreId, " +
                "recordVersion=$recordVersion, " +
                "eventTimestamp=$eventTimestamp, " +
                "status=$status, " +
                "totalSettlementAmount=" + when {
                    totalSettlementAmount == null -> "null"
                    totalSettlementAmount > BigDecimal.ZERO -> "positive amount"
                    totalSettlementAmount < BigDecimal.ZERO -> "negative amount"
                    else -> "zero amount"
                } +
                ")"
    }
}
