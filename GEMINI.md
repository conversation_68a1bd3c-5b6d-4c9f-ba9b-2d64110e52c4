# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Bank Rabbit Gateway is a message relay service that bridges Oracle Advanced Queuing (AQ) with RabbitMQ for banking transaction processing. The service runs as a Micronaut application with Kotlin and provides both polling and reactive message processing modes.

## Common Commands

### Development
```bash
# Run the main service
./gradlew service:run

# Run the playground module
./gradlew playground:run

# Run using shadow jar
./gradlew service:runShadow

# Build all modules
./gradlew build

# Run tests
./gradlew test

# Run specific module tests
./gradlew service:test
./gradlew playground:test
./gradlew rabbitapi:test

# Clean build
./gradlew clean

# Check for single test
./gradlew service:test --tests "ClassName.methodName"
```

### Docker
```bash
# Build Docker layers
./gradlew service:buildLayers
```

## Architecture

### Message Flow
1. **Oracle AQ** → **Relay Looper** → **RabbitMQ Producers** → **RabbitMQ Exchanges**
2. **RabbitMQ Consumers** → **Business Logic** → **External Services**

### Core Components

- **RelayLooper**: Central orchestrator that manages multiple message relay services
- **MessageRelayService**: Interface for different message type handlers
- **Oracle AQ Integration**: JMS-based connection to Oracle Advanced Queuing
- **RabbitMQ Integration**: AMQP producers and consumers for message routing

### Module Structure

- **service/**: Main application with business logic, configuration, and relay services
- **rabbitapi/**: Shared DTOs and API models for RabbitMQ messages
- **playground/**: Development and testing module
- **benchmark/**: Performance testing tools for AQ load testing

### Configuration

The application uses environment-based configuration with these key areas:

- **Looper Configuration**: Controls which message types are processed (`LOOPER_*_ENABLED`)
- **Database**: Oracle connection via JDBC (`JDBC_URL`, `JDBC_USER`, `JDBC_PASSWORD`)
- **RabbitMQ**: Connection settings (`RABBITMQ_HOST`, `RABBITMQ_PORT`, `RABBITMQ_USER`)
- **Processing Mode**: Polling vs Reactive (`GLOBAL_USE_POLLING`)

## Adding New Message Queues

When adding support for a new Oracle AQ queue, follow these steps:

1. **Create MessageRelayService Implementation**
   - Add new implementation in `service/impl/MessageRelay*ServiceImpl.kt`
   - Implement both polling and reactive processing modes

2. **Update RelayLooper**
   - Add new case in `RelayLooper#loopRead()` method
   - Add new case in `RelayLooper#initReactiveConsumer()` method

3. **Configuration Updates**
   - Add looper config in `application.yml`: `looper.queuename.enabled`
   - Increase connection pool sizes:
     - `application.yml`: `datasources.default.maximumPoolSize += 1`
     - `values.yml`: `app.data.MAX_ORACLE_POOLSIZE += 1`

4. **RabbitMQ Configuration**
   - Add producer config: `application.yml.rabbitmq.producers.<exchange>`
   - Add consumer config: `application.yml.rabbitmq.consumers.<exchange>.max-retry`
   - Follow naming convention: `bank-rabbit-gateway-incoming-<queue-name>`

## Technology Stack

- **Kotlin 1.7.20** with JDK 11
- **Micronaut 3.10.1** for dependency injection and web framework
- **Gradle** with Kotlin DSL for build management
- **Oracle AQ** via JMS for message queuing
- **RabbitMQ** with AMQP for message routing
- **Coroutines** for asynchronous processing
- **HikariCP** for database connection pooling
- **Micrometer + Prometheus** for metrics
- **Zipkin** for distributed tracing

## Testing

- **JUnit 5** with Kotlin test DSL
- **Testcontainers** with Oracle Free for integration testing
- **MockK** for Kotlin-friendly mocking
- Integration tests use Oracle dialect with dedicated test profiles

## Deployment

The service is deployed using:
- **Kubernetes** with Helm charts in `service/chart/`
- **ArgoCD** for GitOps deployment
- Environment-specific configurations in `values-*.yml`
- Health checks and monitoring via Micronaut endpoints